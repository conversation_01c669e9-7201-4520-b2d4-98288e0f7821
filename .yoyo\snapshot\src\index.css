@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

:root {
  font-family: 'Raj<PERSON>ni', 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  background: #000000;
  color: #ffffff;
  overflow-x: hidden;
  font-family: 'Rajdhani', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
}

#root {
  width: 100%;
  min-height: 100vh;
}

a {
  color: #00ffff;
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: #ffffff;
  text-shadow: 0 0 10px #00ffff;
}

button {
  font-family: 'Raj<PERSON>ni', sans-serif;
  font-weight: 600;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00ffff, #ffffff);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #ffffff, #00ffff);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
