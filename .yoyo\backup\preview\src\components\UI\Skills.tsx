import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const SkillsContainer = styled.section`
  min-height: 100vh;
  padding: 100px 2rem 50px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 40, 80, 0.8) 100%);
  position: relative;
`;

const SkillsContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled(motion.h2)`
  font-size: 3rem;
  color: #00ffff;
  text-align: center;
  margin-bottom: 3rem;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
`;

const SkillsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-top: 2rem;
`;

const SkillCategory = styled(motion.div)`
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00ffff, #ffffff, #00ffff);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
  }

  @keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
`;

const CategoryTitle = styled.h3`
  font-size: 1.5rem;
  color: #00ffff;
  margin-bottom: 1.5rem;
  font-family: 'Orbitron', monospace;
  text-align: center;
`;

const SkillsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SkillItem = styled(motion.div)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const SkillName = styled.span`
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 1.1rem;
`;

const SkillLevel = styled.span`
  color: #00ffff;
  font-weight: 700;
  font-size: 0.9rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  margin-top: 0.5rem;
  overflow: hidden;
  position: relative;
`;

const ProgressFill = styled(motion.div)<{ level: number }>`
  height: 100%;
  background: linear-gradient(90deg, #00ffff, #ffffff);
  border-radius: 4px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s ease-in-out infinite;
  }

  @keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
`;

const CircuitDecoration = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  opacity: 0.3;
`;

const Skills: React.FC = () => {
  const skillCategories = [
    {
      title: "Frontend Development",
      skills: [
        { name: "React/Next.js", level: 95 },
        { name: "TypeScript", level: 90 },
        { name: "Three.js/WebGL", level: 85 },
        { name: "CSS/Styled Components", level: 92 },
        { name: "Vue.js", level: 80 }
      ]
    },
    {
      title: "Backend Development",
      skills: [
        { name: "Node.js", level: 88 },
        { name: "Python", level: 85 },
        { name: "GraphQL", level: 82 },
        { name: "PostgreSQL", level: 80 },
        { name: "MongoDB", level: 78 }
      ]
    },
    {
      title: "3D & Graphics",
      skills: [
        { name: "Three.js", level: 90 },
        { name: "Blender", level: 75 },
        { name: "WebGL/GLSL", level: 80 },
        { name: "Canvas API", level: 85 },
        { name: "WebXR", level: 70 }
      ]
    },
    {
      title: "Tools & Technologies",
      skills: [
        { name: "Git/GitHub", level: 95 },
        { name: "Docker", level: 82 },
        { name: "AWS/Cloud", level: 78 },
        { name: "Webpack/Vite", level: 85 },
        { name: "Jest/Testing", level: 80 }
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const categoryVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const skillVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <SkillsContainer id="skills">
      <SkillsContent>
        <Title
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          Technical Skills
        </Title>

        <SkillsGrid>
          {skillCategories.map((category, categoryIndex) => (
            <SkillCategory
              key={category.title}
              variants={categoryVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              transition={{ delay: categoryIndex * 0.1 }}
            >
              <CircuitDecoration>
                <svg viewBox="0 0 40 40" fill="none">
                  <path
                    d="M10 20 L30 20 M20 10 L20 30 M15 15 L25 25 M25 15 L15 25"
                    stroke="#00ffff"
                    strokeWidth="1"
                    opacity="0.5"
                  />
                  <circle cx="20" cy="20" r="3" fill="#00ffff" opacity="0.7" />
                </svg>
              </CircuitDecoration>

              <CategoryTitle>{category.title}</CategoryTitle>
              
              <SkillsList>
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill.name}
                    variants={skillVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    transition={{ delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                  >
                    <SkillItem>
                      <SkillName>{skill.name}</SkillName>
                      <SkillLevel>{skill.level}%</SkillLevel>
                    </SkillItem>
                    <ProgressBar>
                      <ProgressFill
                        level={skill.level}
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{ duration: 1, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                        viewport={{ once: true }}
                      />
                    </ProgressBar>
                  </motion.div>
                ))}
              </SkillsList>
            </SkillCategory>
          ))}
        </SkillsGrid>
      </SkillsContent>
    </SkillsContainer>
  );
};

export default Skills;
