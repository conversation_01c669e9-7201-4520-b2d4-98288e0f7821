import React, { useState } from 'react';
import styled from 'styled-components';

const NavContainer = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  padding: 1rem 2rem;
`;

const NavContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
`;

const Logo = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 10px #00ffff;
  cursor: pointer;
`;

const NavLinks = styled.ul`
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
`;

const NavLink = styled.li`
  a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 4px;

    &:hover {
      color: #00ffff;
      border-color: #00ffff;
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.1), transparent);
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 4px;
    }

    &:hover::before {
      opacity: 1;
    }
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: 1px solid #00ffff;
  color: #00ffff;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const MobileMenu = styled.div<{ isOpen: boolean }>`
  display: none;
  
  @media (max-width: 768px) {
    display: ${props => props.isOpen ? 'block' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    border-top: 1px solid rgba(0, 255, 255, 0.3);
    padding: 1rem;
    
    ul {
      flex-direction: column;
      gap: 1rem;
    }
  }
`;

const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { href: '#home', label: 'Home' },
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Projects' },
    { href: '#skills', label: 'Skills' },
    { href: '#contact', label: 'Contact' }
  ];

  return (
    <NavContainer>
      <NavContent>
        <Logo>MICROCHIP.DEV</Logo>
        
        <NavLinks style={{ display: isMobileMenuOpen ? 'none' : 'flex' }}>
          {navItems.map((item) => (
            <NavLink key={item.href}>
              <a href={item.href}>{item.label}</a>
            </NavLink>
          ))}
        </NavLinks>

        <MobileMenuButton 
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          ☰
        </MobileMenuButton>
      </NavContent>

      <MobileMenu isOpen={isMobileMenuOpen}>
        <NavLinks>
          {navItems.map((item) => (
            <NavLink key={item.href}>
              <a href={item.href} onClick={() => setIsMobileMenuOpen(false)}>
                {item.label}
              </a>
            </NavLink>
          ))}
        </NavLinks>
      </MobileMenu>
    </NavContainer>
  );
};

export default Navigation;
