import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const ProjectsContainer = styled.section`
  min-height: 100vh;
  padding: 100px 2rem 50px;
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
  position: relative;
`;

const ProjectsContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled(motion.h2)`
  font-size: 3rem;
  color: #00ffff;
  text-align: center;
  margin-bottom: 3rem;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
`;

const ProjectsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const ProjectCard = styled(motion.div)`
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    transform: translateY(-5px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }
`;

const ProjectImage = styled.div`
  width: 100%;
  height: 200px;
  background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: rgba(0, 255, 255, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
`;

const ProjectTitle = styled.h3`
  font-size: 1.5rem;
  color: #00ffff;
  margin-bottom: 1rem;
  font-family: 'Orbitron', monospace;
`;

const ProjectDescription = styled.p`
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
`;

const TechStack = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
`;

const TechTag = styled.span`
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
`;

const ProjectLinks = styled.div`
  display: flex;
  gap: 1rem;
`;

const ProjectLink = styled.a`
  background: transparent;
  border: 1px solid #00ffff;
  color: #00ffff;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }
`;

const Projects: React.FC = () => {
  const projects = [
    {
      id: 1,
      title: "3D Portfolio Website",
      description: "An immersive 3D portfolio website built with React Three Fiber, featuring interactive microchip-themed elements and smooth animations.",
      tech: ["React", "Three.js", "TypeScript", "Framer Motion"],
      icon: "🔬",
      github: "#",
      demo: "#"
    },
    {
      id: 2,
      title: "Neural Network Visualizer",
      description: "Interactive 3D visualization of neural networks with real-time training data and customizable architecture parameters.",
      tech: ["WebGL", "D3.js", "Python", "TensorFlow"],
      icon: "🧠",
      github: "#",
      demo: "#"
    },
    {
      id: 3,
      title: "Quantum Circuit Simulator",
      description: "A web-based quantum circuit simulator with drag-and-drop interface and real-time quantum state visualization.",
      tech: ["Vue.js", "WebAssembly", "Rust", "Canvas API"],
      icon: "⚛️",
      github: "#",
      demo: "#"
    },
    {
      id: 4,
      title: "Cyberpunk Dashboard",
      description: "Futuristic data dashboard with real-time analytics, holographic charts, and cyberpunk-inspired UI elements.",
      tech: ["React", "Chart.js", "Socket.io", "Node.js"],
      icon: "📊",
      github: "#",
      demo: "#"
    },
    {
      id: 5,
      title: "AR Code Editor",
      description: "Augmented reality code editor that allows developers to write and debug code in 3D space using hand gestures.",
      tech: ["WebXR", "Monaco Editor", "Three.js", "MediaPipe"],
      icon: "🥽",
      github: "#",
      demo: "#"
    },
    {
      id: 6,
      title: "Blockchain Visualizer",
      description: "Interactive 3D blockchain explorer with real-time transaction visualization and network topology mapping.",
      tech: ["React", "Web3.js", "Three.js", "Ethereum"],
      icon: "⛓️",
      github: "#",
      demo: "#"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <ProjectsContainer id="projects">
      <ProjectsContent>
        <Title
          initial={{ opacity: 0, y: -50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          Featured Projects
        </Title>

        <ProjectsGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {projects.map((project) => (
            <ProjectCard
              key={project.id}
              variants={cardVariants}
              whileHover={{ scale: 1.02 }}
            >
              <ProjectImage>
                {project.icon}
              </ProjectImage>
              
              <ProjectTitle>{project.title}</ProjectTitle>
              
              <ProjectDescription>
                {project.description}
              </ProjectDescription>
              
              <TechStack>
                {project.tech.map((tech) => (
                  <TechTag key={tech}>{tech}</TechTag>
                ))}
              </TechStack>
              
              <ProjectLinks>
                <ProjectLink href={project.github} target="_blank">
                  GitHub
                </ProjectLink>
                <ProjectLink href={project.demo} target="_blank">
                  Live Demo
                </ProjectLink>
              </ProjectLinks>
            </ProjectCard>
          ))}
        </ProjectsGrid>
      </ProjectsContent>
    </ProjectsContainer>
  );
};

export default Projects;
