import React from 'react';
import styled, { createGlobalStyle } from 'styled-components';
import Navigation from './components/UI/Navigation';
import Hero from './components/UI/Hero';
import About from './components/UI/About';
import Projects from './components/UI/Projects';
import Skills from './components/UI/Skills';
import Contact from './components/UI/Contact';
import Footer from './components/UI/Footer';
import MicrochipScene from './components/3D/MicrochipScene';

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Arial', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow-x: hidden;
    line-height: 1.6;
  }

  html {
    scroll-behavior: smooth;
  }

  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.5);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #00ffff, #ffffff);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ffffff, #00ffff);
  }
`;

const AppContainer = styled.div`
  position: relative;
  min-height: 100vh;
`;

const ContentWrapper = styled.div`
  position: relative;
  z-index: 1;
`;

function App() {
  return (
    <AppContainer>
      <GlobalStyle />
      <MicrochipScene />
      <ContentWrapper>
        <Navigation />
        <Hero />
        <About />
        <Projects />
        <Skills />
        <Contact />
        <Footer />
      </ContentWrapper>
    </AppContainer>
  );
}

export default App;
