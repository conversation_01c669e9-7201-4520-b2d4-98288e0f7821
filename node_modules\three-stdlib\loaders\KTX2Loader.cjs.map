{"version": 3, "file": "KTX2Loader.cjs", "sources": ["../../src/loaders/KTX2Loader.js"], "sourcesContent": ["/**\n * Loader for KTX 2.0 GPU Texture containers.\n *\n * KTX 2.0 is a container format for various GPU texture formats. The loader\n * supports Basis Universal GPU textures, which can be quickly transcoded to\n * a wide variety of GPU texture compression formats, as well as some\n * uncompressed DataTexture and Data3DTexture formats.\n *\n * References:\n * - KTX: http://github.khronos.org/KTX-Specification/\n * - DFD: https://www.khronos.org/registry/DataFormat/specs/1.3/dataformat.1.3.html#basicdescriptor\n */\n\nimport {\n  CompressedTexture,\n  DataTexture,\n  FileLoader,\n  FloatType,\n  HalfFloatType,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  Loader,\n  RedFormat,\n  RGB_ETC1_Format,\n  RGB_ETC2_Format,\n  RGB_PVRTC_4BPPV1_Format,\n  RGB_S3TC_DXT1_Format,\n  RGBA_ASTC_4x4_Format,\n  RGBA_ASTC_6x6_Format,\n  RGBA_BPTC_Format,\n  RGBA_ETC2_EAC_Format,\n  RGBA_PVRTC_4BPPV1_Format,\n  RGBA_S3TC_DXT5_Format,\n  RGBAFormat,\n  RGFormat,\n  UnsignedByteType,\n} from 'three'\nimport { WorkerPool } from '../utils/WorkerPool'\nimport {\n  read,\n  KHR_DF_FLAG_ALPHA_PREMULTIPLIED,\n  KHR_DF_TRANSFER_SRGB,\n  KHR_SUPERCOMPRESSION_NONE,\n  KHR_SUPERCOMPRESSION_ZSTD,\n  VK_FORMAT_UNDEFINED,\n  VK_FORMAT_R16_SFLOAT,\n  VK_FORMAT_R16G16_SFLOAT,\n  VK_FORMAT_R16G16B16A16_SFLOAT,\n  VK_FORMAT_R32_SFLOAT,\n  VK_FORMAT_R32G32_SFLOAT,\n  VK_FORMAT_R32G32B32A32_SFLOAT,\n  VK_FORMAT_R8_SRGB,\n  VK_FORMAT_R8_UNORM,\n  VK_FORMAT_R8G8_SRGB,\n  VK_FORMAT_R8G8_UNORM,\n  VK_FORMAT_R8G8B8A8_SRGB,\n  VK_FORMAT_R8G8B8A8_UNORM,\n  VK_FORMAT_ASTC_6x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_6x6_UNORM_BLOCK,\n  KHR_DF_PRIMARIES_UNSPECIFIED,\n  KHR_DF_PRIMARIES_BT709,\n  KHR_DF_PRIMARIES_DISPLAYP3,\n} from '../libs/ktx-parse'\nimport { ZSTDDecoder } from '../libs/zstddec'\nimport { CompressedCubeTexture } from '../_polyfill/CompressedCubeTexture'\nimport { CompressedArrayTexture } from '../_polyfill/CompressedArrayTexture'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nconst LinearEncoding = 3000\nconst sRGBEncoding = 3001\n\nconst NoColorSpace = ''\nconst DisplayP3ColorSpace = 'display-p3'\nconst LinearDisplayP3ColorSpace = 'display-p3-linear'\nconst LinearSRGBColorSpace = 'srgb-linear'\nconst SRGBColorSpace = 'srgb'\n\nconst _taskCache = new WeakMap()\n\nlet _activeLoaders = 0\n\nlet _zstd\n\nconst KTX2Loader = /* @__PURE__ */ (() => {\n  class KTX2Loader extends Loader {\n    /* CONSTANTS */\n\n    static BasisFormat = {\n      ETC1S: 0,\n      UASTC_4x4: 1,\n    }\n\n    static TranscoderFormat = {\n      ETC1: 0,\n      ETC2: 1,\n      BC1: 2,\n      BC3: 3,\n      BC4: 4,\n      BC5: 5,\n      BC7_M6_OPAQUE_ONLY: 6,\n      BC7_M5: 7,\n      PVRTC1_4_RGB: 8,\n      PVRTC1_4_RGBA: 9,\n      ASTC_4x4: 10,\n      ATC_RGB: 11,\n      ATC_RGBA_INTERPOLATED_ALPHA: 12,\n      RGBA32: 13,\n      RGB565: 14,\n      BGR565: 15,\n      RGBA4444: 16,\n    }\n\n    static EngineFormat = {\n      RGBAFormat: RGBAFormat,\n      RGBA_ASTC_4x4_Format: RGBA_ASTC_4x4_Format,\n      RGBA_BPTC_Format: RGBA_BPTC_Format,\n      RGBA_ETC2_EAC_Format: RGBA_ETC2_EAC_Format,\n      RGBA_PVRTC_4BPPV1_Format: RGBA_PVRTC_4BPPV1_Format,\n      RGBA_S3TC_DXT5_Format: RGBA_S3TC_DXT5_Format,\n      RGB_ETC1_Format: RGB_ETC1_Format,\n      RGB_ETC2_Format: RGB_ETC2_Format,\n      RGB_PVRTC_4BPPV1_Format: RGB_PVRTC_4BPPV1_Format,\n      RGB_S3TC_DXT1_Format: RGB_S3TC_DXT1_Format,\n    }\n\n    /* WEB WORKER */\n\n    static BasisWorker = function () {\n      let config\n      let transcoderPending\n      let BasisModule\n\n      /** @type KTX2Loader.EngineFormat */\n      const EngineFormat = _EngineFormat\n      /** @type KTX2Loader.TranscoderFormat */\n      const TranscoderFormat = _TranscoderFormat\n      /** @type KTX2Loader.BasisFormat */\n      const BasisFormat = _BasisFormat\n\n      self.addEventListener('message', function (e) {\n        const message = e.data\n\n        switch (message.type) {\n          case 'init':\n            config = message.config\n            init(message.transcoderBinary)\n            break\n\n          case 'transcode':\n            transcoderPending.then(() => {\n              try {\n                const { faces, buffers, width, height, hasAlpha, format, dfdFlags } = transcode(message.buffer)\n\n                self.postMessage(\n                  { type: 'transcode', id: message.id, faces, width, height, hasAlpha, format, dfdFlags },\n                  buffers,\n                )\n              } catch (error) {\n                console.error(error)\n\n                self.postMessage({ type: 'error', id: message.id, error: error.message })\n              }\n            })\n            break\n        }\n      })\n\n      function init(wasmBinary) {\n        transcoderPending = new Promise((resolve) => {\n          BasisModule = { wasmBinary, onRuntimeInitialized: resolve }\n          BASIS(BasisModule)\n        }).then(() => {\n          BasisModule.initializeBasis()\n\n          if (BasisModule.KTX2File === undefined) {\n            console.warn('THREE.KTX2Loader: Please update Basis Universal transcoder.')\n          }\n        })\n      }\n\n      function transcode(buffer) {\n        const ktx2File = new BasisModule.KTX2File(new Uint8Array(buffer))\n\n        function cleanup() {\n          ktx2File.close()\n          ktx2File.delete()\n        }\n\n        if (!ktx2File.isValid()) {\n          cleanup()\n          throw new Error('THREE.KTX2Loader:\tInvalid or unsupported .ktx2 file')\n        }\n\n        const basisFormat = ktx2File.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S\n        const width = ktx2File.getWidth()\n        const height = ktx2File.getHeight()\n        const layerCount = ktx2File.getLayers() || 1\n        const levelCount = ktx2File.getLevels()\n        const faceCount = ktx2File.getFaces()\n        const hasAlpha = ktx2File.getHasAlpha()\n        const dfdFlags = ktx2File.getDFDFlags()\n\n        const { transcoderFormat, engineFormat } = getTranscoderFormat(basisFormat, width, height, hasAlpha)\n\n        if (!width || !height || !levelCount) {\n          cleanup()\n          throw new Error('THREE.KTX2Loader:\tInvalid texture')\n        }\n\n        if (!ktx2File.startTranscoding()) {\n          cleanup()\n          throw new Error('THREE.KTX2Loader: .startTranscoding failed')\n        }\n\n        const faces = []\n        const buffers = []\n\n        for (let face = 0; face < faceCount; face++) {\n          const mipmaps = []\n\n          for (let mip = 0; mip < levelCount; mip++) {\n            const layerMips = []\n\n            let mipWidth, mipHeight\n\n            for (let layer = 0; layer < layerCount; layer++) {\n              const levelInfo = ktx2File.getImageLevelInfo(mip, layer, face)\n\n              if (\n                face === 0 &&\n                mip === 0 &&\n                layer === 0 &&\n                (levelInfo.origWidth % 4 !== 0 || levelInfo.origHeight % 4 !== 0)\n              ) {\n                console.warn('THREE.KTX2Loader: ETC1S and UASTC textures should use multiple-of-four dimensions.')\n              }\n\n              if (levelCount > 1) {\n                mipWidth = levelInfo.origWidth\n                mipHeight = levelInfo.origHeight\n              } else {\n                // Handles non-multiple-of-four dimensions in textures without mipmaps. Textures with\n                // mipmaps must use multiple-of-four dimensions, for some texture formats and APIs.\n                // See mrdoob/three.js#25908.\n                mipWidth = levelInfo.width\n                mipHeight = levelInfo.height\n              }\n\n              const dst = new Uint8Array(ktx2File.getImageTranscodedSizeInBytes(mip, layer, 0, transcoderFormat))\n              const status = ktx2File.transcodeImage(dst, mip, layer, face, transcoderFormat, 0, -1, -1)\n\n              if (!status) {\n                cleanup()\n                throw new Error('THREE.KTX2Loader: .transcodeImage failed.')\n              }\n\n              layerMips.push(dst)\n            }\n\n            const mipData = concat(layerMips)\n\n            mipmaps.push({ data: mipData, width: mipWidth, height: mipHeight })\n            buffers.push(mipData.buffer)\n          }\n\n          faces.push({ mipmaps, width, height, format: engineFormat })\n        }\n\n        cleanup()\n\n        return { faces, buffers, width, height, hasAlpha, format: engineFormat, dfdFlags }\n      }\n\n      //\n\n      // Optimal choice of a transcoder target format depends on the Basis format (ETC1S or UASTC),\n      // device capabilities, and texture dimensions. The list below ranks the formats separately\n      // for ETC1S and UASTC.\n      //\n      // In some cases, transcoding UASTC to RGBA32 might be preferred for higher quality (at\n      // significant memory cost) compared to ETC1/2, BC1/3, and PVRTC. The transcoder currently\n      // chooses RGBA32 only as a last resort and does not expose that option to the caller.\n      const FORMAT_OPTIONS = [\n        {\n          if: 'astcSupported',\n          basisFormat: [BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4],\n          engineFormat: [EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format],\n          priorityETC1S: Infinity,\n          priorityUASTC: 1,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'bptcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5],\n          engineFormat: [EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format],\n          priorityETC1S: 3,\n          priorityUASTC: 2,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'dxtSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC1, TranscoderFormat.BC3],\n          engineFormat: [EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format],\n          priorityETC1S: 4,\n          priorityUASTC: 5,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc2Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC2],\n          engineFormat: [EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format],\n          priorityETC1S: 1,\n          priorityUASTC: 3,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc1Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1],\n          engineFormat: [EngineFormat.RGB_ETC1_Format],\n          priorityETC1S: 2,\n          priorityUASTC: 4,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'pvrtcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA],\n          engineFormat: [EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format],\n          priorityETC1S: 5,\n          priorityUASTC: 6,\n          needsPowerOfTwo: true,\n        },\n      ]\n\n      const ETC1S_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityETC1S - b.priorityETC1S\n      })\n      const UASTC_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityUASTC - b.priorityUASTC\n      })\n\n      function getTranscoderFormat(basisFormat, width, height, hasAlpha) {\n        let transcoderFormat\n        let engineFormat\n\n        const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS\n\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i]\n\n          if (!config[opt.if]) continue\n          if (!opt.basisFormat.includes(basisFormat)) continue\n          if (hasAlpha && opt.transcoderFormat.length < 2) continue\n          if (opt.needsPowerOfTwo && !(isPowerOfTwo(width) && isPowerOfTwo(height))) continue\n\n          transcoderFormat = opt.transcoderFormat[hasAlpha ? 1 : 0]\n          engineFormat = opt.engineFormat[hasAlpha ? 1 : 0]\n\n          return { transcoderFormat, engineFormat }\n        }\n\n        console.warn('THREE.KTX2Loader: No suitable compressed texture format found. Decoding to RGBA32.')\n\n        transcoderFormat = TranscoderFormat.RGBA32\n        engineFormat = EngineFormat.RGBAFormat\n\n        return { transcoderFormat, engineFormat }\n      }\n\n      function isPowerOfTwo(value) {\n        if (value <= 2) return true\n\n        return (value & (value - 1)) === 0 && value !== 0\n      }\n\n      /** Concatenates N byte arrays. */\n      function concat(arrays) {\n        if (arrays.length === 1) return arrays[0]\n\n        let totalByteLength = 0\n\n        for (let i = 0; i < arrays.length; i++) {\n          const array = arrays[i]\n          totalByteLength += array.byteLength\n        }\n\n        const result = new Uint8Array(totalByteLength)\n\n        let byteOffset = 0\n\n        for (let i = 0; i < arrays.length; i++) {\n          const array = arrays[i]\n          result.set(array, byteOffset)\n\n          byteOffset += array.byteLength\n        }\n\n        return result\n      }\n    }\n\n    constructor(manager) {\n      super(manager)\n\n      this.transcoderPath = ''\n      this.transcoderBinary = null\n      this.transcoderPending = null\n\n      this.workerPool = new WorkerPool()\n      this.workerSourceURL = ''\n      this.workerConfig = null\n\n      if (typeof MSC_TRANSCODER !== 'undefined') {\n        console.warn(\n          'THREE.KTX2Loader: Please update to latest \"basis_transcoder\".' +\n            ' \"msc_basis_transcoder\" is no longer supported in three.js r125+.',\n        )\n      }\n    }\n\n    setTranscoderPath(path) {\n      this.transcoderPath = path\n\n      return this\n    }\n\n    setWorkerLimit(num) {\n      this.workerPool.setWorkerLimit(num)\n\n      return this\n    }\n\n    detectSupport(renderer) {\n      this.workerConfig = {\n        astcSupported: renderer.extensions.has('WEBGL_compressed_texture_astc'),\n        etc1Supported: renderer.extensions.has('WEBGL_compressed_texture_etc1'),\n        etc2Supported: renderer.extensions.has('WEBGL_compressed_texture_etc'),\n        dxtSupported: renderer.extensions.has('WEBGL_compressed_texture_s3tc'),\n        bptcSupported: renderer.extensions.has('EXT_texture_compression_bptc'),\n        pvrtcSupported:\n          renderer.extensions.has('WEBGL_compressed_texture_pvrtc') ||\n          renderer.extensions.has('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n      }\n\n      if (renderer.capabilities.isWebGL2) {\n        // https://github.com/mrdoob/three.js/pull/22928\n        this.workerConfig.etc1Supported = false\n      }\n\n      return this\n    }\n\n    init() {\n      if (!this.transcoderPending) {\n        // Load transcoder wrapper.\n        const jsLoader = new FileLoader(this.manager)\n        jsLoader.setPath(this.transcoderPath)\n        jsLoader.setWithCredentials(this.withCredentials)\n        const jsContent = jsLoader.loadAsync('basis_transcoder.js')\n\n        // Load transcoder WASM binary.\n        const binaryLoader = new FileLoader(this.manager)\n        binaryLoader.setPath(this.transcoderPath)\n        binaryLoader.setResponseType('arraybuffer')\n        binaryLoader.setWithCredentials(this.withCredentials)\n        const binaryContent = binaryLoader.loadAsync('basis_transcoder.wasm')\n\n        this.transcoderPending = Promise.all([jsContent, binaryContent]).then(([jsContent, binaryContent]) => {\n          const fn = KTX2Loader.BasisWorker.toString()\n\n          const body = [\n            '/* constants */',\n            'let _EngineFormat = ' + JSON.stringify(KTX2Loader.EngineFormat),\n            'let _TranscoderFormat = ' + JSON.stringify(KTX2Loader.TranscoderFormat),\n            'let _BasisFormat = ' + JSON.stringify(KTX2Loader.BasisFormat),\n            '/* basis_transcoder.js */',\n            jsContent,\n            '/* worker */',\n            fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n          ].join('\\n')\n\n          this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n          this.transcoderBinary = binaryContent\n\n          this.workerPool.setWorkerCreator(() => {\n            const worker = new Worker(this.workerSourceURL)\n            const transcoderBinary = this.transcoderBinary.slice(0)\n\n            worker.postMessage({ type: 'init', config: this.workerConfig, transcoderBinary }, [transcoderBinary])\n\n            return worker\n          })\n        })\n\n        if (_activeLoaders > 0) {\n          // Each instance loads a transcoder and allocates workers, increasing network and memory cost.\n\n          console.warn(\n            'THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues.' +\n              ' Use a single KTX2Loader instance, or call .dispose() on old instances.',\n          )\n        }\n\n        _activeLoaders++\n      }\n\n      return this.transcoderPending\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      if (this.workerConfig === null) {\n        throw new Error('THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.')\n      }\n\n      const loader = new FileLoader(this.manager)\n\n      loader.setResponseType('arraybuffer')\n      loader.setWithCredentials(this.withCredentials)\n\n      loader.load(\n        url,\n        (buffer) => {\n          // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n          // again from this thread.\n          if (_taskCache.has(buffer)) {\n            const cachedTask = _taskCache.get(buffer)\n\n            return cachedTask.promise.then(onLoad).catch(onError)\n          }\n\n          this._createTexture(buffer)\n            .then((texture) => (onLoad ? onLoad(texture) : null))\n            .catch(onError)\n        },\n        onProgress,\n        onError,\n      )\n    }\n\n    _createTextureFrom(transcodeResult, container) {\n      const { faces, width, height, format, type, error, dfdFlags } = transcodeResult\n\n      if (type === 'error') return Promise.reject(error)\n\n      let texture\n\n      if (container.faceCount === 6) {\n        texture = new CompressedCubeTexture(faces, format, UnsignedByteType)\n      } else {\n        const mipmaps = faces[0].mipmaps\n\n        texture =\n          container.layerCount > 1\n            ? new CompressedArrayTexture(mipmaps, width, height, container.layerCount, format, UnsignedByteType)\n            : new CompressedTexture(mipmaps, width, height, format, UnsignedByteType)\n      }\n\n      texture.minFilter = faces[0].mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter\n      texture.magFilter = LinearFilter\n      texture.generateMipmaps = false\n      texture.needsUpdate = true\n\n      const colorSpace = parseColorSpace(container)\n      if ('colorSpace' in texture) texture.colorSpace = colorSpace\n      else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n\n      texture.premultiplyAlpha = !!(dfdFlags & KHR_DF_FLAG_ALPHA_PREMULTIPLIED)\n\n      return texture\n    }\n\n    /**\n     * @param {ArrayBuffer} buffer\n     * @param {object?} config\n     * @return {Promise<CompressedTexture|CompressedArrayTexture|DataTexture|Data3DTexture>}\n     */\n    async _createTexture(buffer, config = {}) {\n      const container = read(new Uint8Array(buffer))\n\n      if (container.vkFormat !== VK_FORMAT_UNDEFINED) {\n        return createRawTexture(container)\n      }\n\n      //\n\n      const taskConfig = config\n      const texturePending = this.init()\n        .then(() => {\n          return this.workerPool.postMessage({ type: 'transcode', buffer, taskConfig: taskConfig }, [buffer])\n        })\n        .then((e) => this._createTextureFrom(e.data, container))\n\n      // Cache the task result.\n      _taskCache.set(buffer, { promise: texturePending })\n\n      return texturePending\n    }\n\n    dispose() {\n      this.workerPool.dispose()\n      if (this.workerSourceURL) URL.revokeObjectURL(this.workerSourceURL)\n\n      _activeLoaders--\n\n      return this\n    }\n  }\n\n  return KTX2Loader\n})()\n\n//\n// Parsing for non-Basis textures. These textures are may have supercompression\n// like Zstd, but they do not require transcoding.\n\nconst UNCOMPRESSED_FORMATS = new Set([RGBAFormat, RGFormat, RedFormat])\n\nconst FORMAT_MAP = {\n  [VK_FORMAT_R32G32B32A32_SFLOAT]: RGBAFormat,\n  [VK_FORMAT_R16G16B16A16_SFLOAT]: RGBAFormat,\n  [VK_FORMAT_R8G8B8A8_UNORM]: RGBAFormat,\n  [VK_FORMAT_R8G8B8A8_SRGB]: RGBAFormat,\n\n  [VK_FORMAT_R32G32_SFLOAT]: RGFormat,\n  [VK_FORMAT_R16G16_SFLOAT]: RGFormat,\n  [VK_FORMAT_R8G8_UNORM]: RGFormat,\n  [VK_FORMAT_R8G8_SRGB]: RGFormat,\n\n  [VK_FORMAT_R32_SFLOAT]: RedFormat,\n  [VK_FORMAT_R16_SFLOAT]: RedFormat,\n  [VK_FORMAT_R8_SRGB]: RedFormat,\n  [VK_FORMAT_R8_UNORM]: RedFormat,\n\n  [VK_FORMAT_ASTC_6x6_SRGB_BLOCK]: RGBA_ASTC_6x6_Format,\n  [VK_FORMAT_ASTC_6x6_UNORM_BLOCK]: RGBA_ASTC_6x6_Format,\n}\n\nconst TYPE_MAP = {\n  [VK_FORMAT_R32G32B32A32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16G16B16A16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8G8B8A8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_R8G8B8A8_SRGB]: UnsignedByteType,\n\n  [VK_FORMAT_R32G32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16G16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8G8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_R8G8_SRGB]: UnsignedByteType,\n\n  [VK_FORMAT_R32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8_SRGB]: UnsignedByteType,\n  [VK_FORMAT_R8_UNORM]: UnsignedByteType,\n\n  [VK_FORMAT_ASTC_6x6_SRGB_BLOCK]: UnsignedByteType,\n  [VK_FORMAT_ASTC_6x6_UNORM_BLOCK]: UnsignedByteType,\n}\n\nasync function createRawTexture(container) {\n  const { vkFormat } = container\n\n  if (FORMAT_MAP[vkFormat] === undefined) {\n    throw new Error('THREE.KTX2Loader: Unsupported vkFormat.')\n  }\n\n  //\n\n  let zstd\n\n  if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_ZSTD) {\n    if (!_zstd) {\n      _zstd = new Promise(async (resolve) => {\n        const zstd = new ZSTDDecoder()\n        await zstd.init()\n        resolve(zstd)\n      })\n    }\n\n    zstd = await _zstd\n  }\n\n  //\n\n  const mipmaps = []\n\n  for (let levelIndex = 0; levelIndex < container.levels.length; levelIndex++) {\n    const levelWidth = Math.max(1, container.pixelWidth >> levelIndex)\n    const levelHeight = Math.max(1, container.pixelHeight >> levelIndex)\n    const levelDepth = container.pixelDepth ? Math.max(1, container.pixelDepth >> levelIndex) : 0\n\n    const level = container.levels[levelIndex]\n\n    let levelData\n\n    if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_NONE) {\n      levelData = level.levelData\n    } else if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_ZSTD) {\n      levelData = zstd.decode(level.levelData, level.uncompressedByteLength)\n    } else {\n      throw new Error('THREE.KTX2Loader: Unsupported supercompressionScheme.')\n    }\n\n    let data\n\n    if (TYPE_MAP[vkFormat] === FloatType) {\n      data = new Float32Array(\n        levelData.buffer,\n        levelData.byteOffset,\n        levelData.byteLength / Float32Array.BYTES_PER_ELEMENT,\n      )\n    } else if (TYPE_MAP[vkFormat] === HalfFloatType) {\n      data = new Uint16Array(\n        levelData.buffer,\n        levelData.byteOffset,\n        levelData.byteLength / Uint16Array.BYTES_PER_ELEMENT,\n      )\n    } else {\n      data = levelData\n    }\n\n    mipmaps.push({\n      data: data,\n      width: levelWidth,\n      height: levelHeight,\n      depth: levelDepth,\n    })\n  }\n\n  let texture\n\n  if (UNCOMPRESSED_FORMATS.has(FORMAT_MAP[vkFormat])) {\n    texture =\n      container.pixelDepth === 0\n        ? new DataTexture(mipmaps[0].data, container.pixelWidth, container.pixelHeight)\n        : new Data3DTexture(mipmaps[0].data, container.pixelWidth, container.pixelHeight, container.pixelDepth)\n  } else {\n    if (container.pixelDepth > 0) throw new Error('THREE.KTX2Loader: Unsupported pixelDepth.')\n\n    texture = new CompressedTexture(mipmaps, container.pixelWidth, container.pixelHeight)\n  }\n\n  texture.mipmaps = mipmaps\n\n  texture.type = TYPE_MAP[vkFormat]\n  texture.format = FORMAT_MAP[vkFormat]\n  texture.needsUpdate = true\n\n  const colorSpace = parseColorSpace(container)\n  if ('colorSpace' in texture) texture.colorSpace = colorSpace\n  else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n\n  //\n\n  return Promise.resolve(texture)\n}\n\nfunction parseColorSpace(container) {\n  const dfd = container.dataFormatDescriptor[0]\n\n  if (dfd.colorPrimaries === KHR_DF_PRIMARIES_BT709) {\n    return dfd.transferFunction === KHR_DF_TRANSFER_SRGB ? SRGBColorSpace : LinearSRGBColorSpace\n  } else if (dfd.colorPrimaries === KHR_DF_PRIMARIES_DISPLAYP3) {\n    return dfd.transferFunction === KHR_DF_TRANSFER_SRGB ? DisplayP3ColorSpace : LinearDisplayP3ColorSpace\n  } else if (dfd.colorPrimaries === KHR_DF_PRIMARIES_UNSPECIFIED) {\n    return NoColorSpace\n  } else {\n    console.warn(`THREE.KTX2Loader: Unsupported color primaries, \"${dfd.colorPrimaries}\"`)\n    return NoColorSpace\n  }\n}\n\nexport { KTX2Loader }\n"], "names": ["Loader", "WorkerPool", "<PERSON><PERSON><PERSON><PERSON>", "js<PERSON><PERSON><PERSON>", "binaryContent", "CompressedCubeTexture", "UnsignedByteType", "CompressedArrayTexture", "CompressedTexture", "LinearFilter", "LinearMipmapLinearFilter", "KHR_DF_FLAG_ALPHA_PREMULTIPLIED", "read", "VK_FORMAT_UNDEFINED", "KTX2Loader", "RGBAFormat", "RGBA_ASTC_4x4_Format", "RGBA_BPTC_Format", "RGBA_ETC2_EAC_Format", "RGBA_PVRTC_4BPPV1_Format", "RGBA_S3TC_DXT5_Format", "RGB_ETC1_Format", "RGB_ETC2_Format", "RGB_PVRTC_4BPPV1_Format", "RGB_S3TC_DXT1_Format", "RGFormat", "RedFormat", "VK_FORMAT_R32G32B32A32_SFLOAT", "VK_FORMAT_R16G16B16A16_SFLOAT", "VK_FORMAT_R8G8B8A8_UNORM", "VK_FORMAT_R8G8B8A8_SRGB", "VK_FORMAT_R32G32_SFLOAT", "VK_FORMAT_R16G16_SFLOAT", "VK_FORMAT_R8G8_UNORM", "VK_FORMAT_R8G8_SRGB", "VK_FORMAT_R32_SFLOAT", "VK_FORMAT_R16_SFLOAT", "VK_FORMAT_R8_SRGB", "VK_FORMAT_R8_UNORM", "VK_FORMAT_ASTC_6x6_SRGB_BLOCK", "RGBA_ASTC_6x6_Format", "VK_FORMAT_ASTC_6x6_UNORM_BLOCK", "FloatType", "HalfFloatType", "KHR_SUPERCOMPRESSION_ZSTD", "zstd", "ZSTDDecoder", "KHR_SUPERCOMPRESSION_NONE", "DataTexture", "Data3DTexture", "KHR_DF_PRIMARIES_BT709", "KHR_DF_TRANSFER_SRGB", "KHR_DF_PRIMARIES_DISPLAYP3", "KHR_DF_PRIMARIES_UNSPECIFIED"], "mappings": ";;;;;;;;;;;;;;;AAoEA,MAAM,iBAAiB;AACvB,MAAM,eAAe;AAErB,MAAM,eAAe;AACrB,MAAM,sBAAsB;AAC5B,MAAM,4BAA4B;AAClC,MAAM,uBAAuB;AAC7B,MAAM,iBAAiB;AAEvB,MAAM,aAAa,oBAAI,QAAS;AAEhC,IAAI,iBAAiB;AAErB,IAAI;AAEC,MAAC,aAA8B,uBAAM;AACxC,QAAM,cAAN,cAAyBA,MAAAA,OAAO;AAAA,IAkU9B,YAAY,SAAS;AACnB,YAAM,OAAO;AAEb,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AAEzB,WAAK,aAAa,IAAIC,sBAAY;AAClC,WAAK,kBAAkB;AACvB,WAAK,eAAe;AAEpB,UAAI,OAAO,mBAAmB,aAAa;AACzC,gBAAQ;AAAA,UACN;AAAA,QAED;AAAA,MACF;AAAA,IACF;AAAA,IAED,kBAAkB,MAAM;AACtB,WAAK,iBAAiB;AAEtB,aAAO;AAAA,IACR;AAAA,IAED,eAAe,KAAK;AAClB,WAAK,WAAW,eAAe,GAAG;AAElC,aAAO;AAAA,IACR;AAAA,IAED,cAAc,UAAU;AACtB,WAAK,eAAe;AAAA,QAClB,eAAe,SAAS,WAAW,IAAI,+BAA+B;AAAA,QACtE,eAAe,SAAS,WAAW,IAAI,+BAA+B;AAAA,QACtE,eAAe,SAAS,WAAW,IAAI,8BAA8B;AAAA,QACrE,cAAc,SAAS,WAAW,IAAI,+BAA+B;AAAA,QACrE,eAAe,SAAS,WAAW,IAAI,8BAA8B;AAAA,QACrE,gBACE,SAAS,WAAW,IAAI,gCAAgC,KACxD,SAAS,WAAW,IAAI,uCAAuC;AAAA,MAClE;AAED,UAAI,SAAS,aAAa,UAAU;AAElC,aAAK,aAAa,gBAAgB;AAAA,MACnC;AAED,aAAO;AAAA,IACR;AAAA,IAED,OAAO;AACL,UAAI,CAAC,KAAK,mBAAmB;AAE3B,cAAM,WAAW,IAAIC,iBAAW,KAAK,OAAO;AAC5C,iBAAS,QAAQ,KAAK,cAAc;AACpC,iBAAS,mBAAmB,KAAK,eAAe;AAChD,cAAM,YAAY,SAAS,UAAU,qBAAqB;AAG1D,cAAM,eAAe,IAAIA,iBAAW,KAAK,OAAO;AAChD,qBAAa,QAAQ,KAAK,cAAc;AACxC,qBAAa,gBAAgB,aAAa;AAC1C,qBAAa,mBAAmB,KAAK,eAAe;AACpD,cAAM,gBAAgB,aAAa,UAAU,uBAAuB;AAEpE,aAAK,oBAAoB,QAAQ,IAAI,CAAC,WAAW,aAAa,CAAC,EAAE,KAAK,CAAC,CAACC,YAAWC,cAAa,MAAM;AACpG,gBAAM,KAAK,YAAW,YAAY,SAAU;AAE5C,gBAAM,OAAO;AAAA,YACX;AAAA,YACA,yBAAyB,KAAK,UAAU,YAAW,YAAY;AAAA,YAC/D,6BAA6B,KAAK,UAAU,YAAW,gBAAgB;AAAA,YACvE,wBAAwB,KAAK,UAAU,YAAW,WAAW;AAAA,YAC7D;AAAA,YACAD;AAAA,YACA;AAAA,YACA,GAAG,UAAU,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC;AAAA,UACjE,EAAY,KAAK,IAAI;AAEX,eAAK,kBAAkB,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3D,eAAK,mBAAmBC;AAExB,eAAK,WAAW,iBAAiB,MAAM;AACrC,kBAAM,SAAS,IAAI,OAAO,KAAK,eAAe;AAC9C,kBAAM,mBAAmB,KAAK,iBAAiB,MAAM,CAAC;AAEtD,mBAAO,YAAY,EAAE,MAAM,QAAQ,QAAQ,KAAK,cAAc,oBAAoB,CAAC,gBAAgB,CAAC;AAEpG,mBAAO;AAAA,UACnB,CAAW;AAAA,QACX,CAAS;AAED,YAAI,iBAAiB,GAAG;AAGtB,kBAAQ;AAAA,YACN;AAAA,UAED;AAAA,QACF;AAED;AAAA,MACD;AAED,aAAO,KAAK;AAAA,IACb;AAAA,IAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAI,KAAK,iBAAiB,MAAM;AAC9B,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC9F;AAED,YAAM,SAAS,IAAIF,iBAAW,KAAK,OAAO;AAE1C,aAAO,gBAAgB,aAAa;AACpC,aAAO,mBAAmB,KAAK,eAAe;AAE9C,aAAO;AAAA,QACL;AAAA,QACA,CAAC,WAAW;AAGV,cAAI,WAAW,IAAI,MAAM,GAAG;AAC1B,kBAAM,aAAa,WAAW,IAAI,MAAM;AAExC,mBAAO,WAAW,QAAQ,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,UACrD;AAED,eAAK,eAAe,MAAM,EACvB,KAAK,CAAC,YAAa,SAAS,OAAO,OAAO,IAAI,IAAK,EACnD,MAAM,OAAO;AAAA,QACjB;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,IAED,mBAAmB,iBAAiB,WAAW;AAC7C,YAAM,EAAE,OAAO,OAAO,QAAQ,QAAQ,MAAM,OAAO,SAAQ,IAAK;AAEhE,UAAI,SAAS;AAAS,eAAO,QAAQ,OAAO,KAAK;AAEjD,UAAI;AAEJ,UAAI,UAAU,cAAc,GAAG;AAC7B,kBAAU,IAAIG,sBAAqB,sBAAC,OAAO,QAAQC,MAAAA,gBAAgB;AAAA,MAC3E,OAAa;AACL,cAAM,UAAU,MAAM,CAAC,EAAE;AAEzB,kBACE,UAAU,aAAa,IACnB,IAAIC,uBAAsB,uBAAC,SAAS,OAAO,QAAQ,UAAU,YAAY,QAAQD,sBAAgB,IACjG,IAAIE,MAAAA,kBAAkB,SAAS,OAAO,QAAQ,QAAQF,MAAAA,gBAAgB;AAAA,MAC7E;AAED,cAAQ,YAAY,MAAM,CAAC,EAAE,QAAQ,WAAW,IAAIG,MAAAA,eAAeC,MAAwB;AAC3F,cAAQ,YAAYD,MAAY;AAChC,cAAQ,kBAAkB;AAC1B,cAAQ,cAAc;AAEtB,YAAM,aAAa,gBAAgB,SAAS;AAC5C,UAAI,gBAAgB;AAAS,gBAAQ,aAAa;AAAA;AAC7C,gBAAQ,WAAW,eAAe,iBAAiB,eAAe;AAEvE,cAAQ,mBAAmB,CAAC,EAAE,WAAWE,SAAAA;AAEzC,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,MAAM,eAAe,QAAQ,SAAS,IAAI;AACxC,YAAM,YAAYC,SAAI,KAAC,IAAI,WAAW,MAAM,CAAC;AAE7C,UAAI,UAAU,aAAaC,8BAAqB;AAC9C,eAAO,iBAAiB,SAAS;AAAA,MAClC;AAID,YAAM,aAAa;AACnB,YAAM,iBAAiB,KAAK,KAAM,EAC/B,KAAK,MAAM;AACV,eAAO,KAAK,WAAW,YAAY,EAAE,MAAM,aAAa,QAAQ,cAA0B,CAAC,MAAM,CAAC;AAAA,MAC5G,CAAS,EACA,KAAK,CAAC,MAAM,KAAK,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAGzD,iBAAW,IAAI,QAAQ,EAAE,SAAS,eAAc,CAAE;AAElD,aAAO;AAAA,IACR;AAAA,IAED,UAAU;AACR,WAAK,WAAW,QAAS;AACzB,UAAI,KAAK;AAAiB,YAAI,gBAAgB,KAAK,eAAe;AAElE;AAEA,aAAO;AAAA,IACR;AAAA,EACF;AA/gBD,MAAMC,cAAN;AAGE;AAAA,gBAHIA,aAGG,eAAc;AAAA,IACnB,OAAO;AAAA,IACP,WAAW;AAAA,EACZ;AAED,gBARIA,aAQG,oBAAmB;AAAA,IACxB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,oBAAoB;AAAA,IACpB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,6BAA6B;AAAA,IAC7B,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,EACX;AAED,gBA5BIA,aA4BG,gBAAe;AAAA,IACpB,YAAYC,MAAU;AAAA,IACtB,sBAAsBC,MAAoB;AAAA,IAC1C,kBAAkBC,MAAgB;AAAA,IAClC,sBAAsBC,MAAoB;AAAA,IAC1C,0BAA0BC,MAAwB;AAAA,IAClD,uBAAuBC,MAAqB;AAAA,IAC5C,iBAAiBC,MAAe;AAAA,IAChC,iBAAiBC,MAAe;AAAA,IAChC,yBAAyBC,MAAuB;AAAA,IAChD,sBAAsBC,MAAoB;AAAA,EAC3C;AAID;AAAA,gBA3CIV,aA2CG,eAAc,WAAY;AAC/B,QAAI;AACJ,QAAI;AACJ,QAAI;AAGJ,UAAM,eAAe;AAErB,UAAM,mBAAmB;AAEzB,UAAM,cAAc;AAEpB,SAAK,iBAAiB,WAAW,SAAU,GAAG;AAC5C,YAAM,UAAU,EAAE;AAElB,cAAQ,QAAQ,MAAI;AAAA,QAClB,KAAK;AACH,mBAAS,QAAQ;AACjB,eAAK,QAAQ,gBAAgB;AAC7B;AAAA,QAEF,KAAK;AACH,4BAAkB,KAAK,MAAM;AAC3B,gBAAI;AACF,oBAAM,EAAE,OAAO,SAAS,OAAO,QAAQ,UAAU,QAAQ,SAAU,IAAG,UAAU,QAAQ,MAAM;AAE9F,mBAAK;AAAA,gBACH,EAAE,MAAM,aAAa,IAAI,QAAQ,IAAI,OAAO,OAAO,QAAQ,UAAU,QAAQ,SAAU;AAAA,gBACvF;AAAA,cACD;AAAA,YACF,SAAQ,OAAP;AACA,sBAAQ,MAAM,KAAK;AAEnB,mBAAK,YAAY,EAAE,MAAM,SAAS,IAAI,QAAQ,IAAI,OAAO,MAAM,QAAO,CAAE;AAAA,YACzE;AAAA,UACf,CAAa;AACD;AAAA,MACH;AAAA,IACT,CAAO;AAED,aAAS,KAAK,YAAY;AACxB,0BAAoB,IAAI,QAAQ,CAAC,YAAY;AAC3C,sBAAc,EAAE,YAAY,sBAAsB,QAAS;AAC3D,cAAM,WAAW;AAAA,MAC3B,CAAS,EAAE,KAAK,MAAM;AACZ,oBAAY,gBAAiB;AAE7B,YAAI,YAAY,aAAa,QAAW;AACtC,kBAAQ,KAAK,6DAA6D;AAAA,QAC3E;AAAA,MACX,CAAS;AAAA,IACF;AAED,aAAS,UAAU,QAAQ;AACzB,YAAM,WAAW,IAAI,YAAY,SAAS,IAAI,WAAW,MAAM,CAAC;AAEhE,eAAS,UAAU;AACjB,iBAAS,MAAO;AAChB,iBAAS,OAAQ;AAAA,MAClB;AAED,UAAI,CAAC,SAAS,WAAW;AACvB,gBAAS;AACT,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACtE;AAED,YAAM,cAAc,SAAS,QAAO,IAAK,YAAY,YAAY,YAAY;AAC7E,YAAM,QAAQ,SAAS,SAAU;AACjC,YAAM,SAAS,SAAS,UAAW;AACnC,YAAM,aAAa,SAAS,UAAS,KAAM;AAC3C,YAAM,aAAa,SAAS,UAAW;AACvC,YAAM,YAAY,SAAS,SAAU;AACrC,YAAM,WAAW,SAAS,YAAa;AACvC,YAAM,WAAW,SAAS,YAAa;AAEvC,YAAM,EAAE,kBAAkB,iBAAiB,oBAAoB,aAAa,OAAO,QAAQ,QAAQ;AAEnG,UAAI,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY;AACpC,gBAAS;AACT,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACpD;AAED,UAAI,CAAC,SAAS,oBAAoB;AAChC,gBAAS;AACT,cAAM,IAAI,MAAM,4CAA4C;AAAA,MAC7D;AAED,YAAM,QAAQ,CAAE;AAChB,YAAM,UAAU,CAAE;AAElB,eAAS,OAAO,GAAG,OAAO,WAAW,QAAQ;AAC3C,cAAM,UAAU,CAAE;AAElB,iBAAS,MAAM,GAAG,MAAM,YAAY,OAAO;AACzC,gBAAM,YAAY,CAAE;AAEpB,cAAI,UAAU;AAEd,mBAAS,QAAQ,GAAG,QAAQ,YAAY,SAAS;AAC/C,kBAAM,YAAY,SAAS,kBAAkB,KAAK,OAAO,IAAI;AAE7D,gBACE,SAAS,KACT,QAAQ,KACR,UAAU,MACT,UAAU,YAAY,MAAM,KAAK,UAAU,aAAa,MAAM,IAC/D;AACA,sBAAQ,KAAK,oFAAoF;AAAA,YAClG;AAED,gBAAI,aAAa,GAAG;AAClB,yBAAW,UAAU;AACrB,0BAAY,UAAU;AAAA,YACtC,OAAqB;AAIL,yBAAW,UAAU;AACrB,0BAAY,UAAU;AAAA,YACvB;AAED,kBAAM,MAAM,IAAI,WAAW,SAAS,8BAA8B,KAAK,OAAO,GAAG,gBAAgB,CAAC;AAClG,kBAAM,SAAS,SAAS,eAAe,KAAK,KAAK,OAAO,MAAM,kBAAkB,GAAG,IAAI,EAAE;AAEzF,gBAAI,CAAC,QAAQ;AACX,sBAAS;AACT,oBAAM,IAAI,MAAM,2CAA2C;AAAA,YAC5D;AAED,sBAAU,KAAK,GAAG;AAAA,UACnB;AAED,gBAAM,UAAU,OAAO,SAAS;AAEhC,kBAAQ,KAAK,EAAE,MAAM,SAAS,OAAO,UAAU,QAAQ,WAAW;AAClE,kBAAQ,KAAK,QAAQ,MAAM;AAAA,QAC5B;AAED,cAAM,KAAK,EAAE,SAAS,OAAO,QAAQ,QAAQ,cAAc;AAAA,MAC5D;AAED,cAAS;AAET,aAAO,EAAE,OAAO,SAAS,OAAO,QAAQ,UAAU,QAAQ,cAAc,SAAU;AAAA,IACnF;AAWD,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,SAAS;AAAA,QACnC,kBAAkB,CAAC,iBAAiB,UAAU,iBAAiB,QAAQ;AAAA,QACvE,cAAc,CAAC,aAAa,sBAAsB,aAAa,oBAAoB;AAAA,QACnF,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,QAAQ,iBAAiB,MAAM;AAAA,QACnE,cAAc,CAAC,aAAa,kBAAkB,aAAa,gBAAgB;AAAA,QAC3E,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,KAAK,iBAAiB,GAAG;AAAA,QAC7D,cAAc,CAAC,aAAa,sBAAsB,aAAa,qBAAqB;AAAA,QACpF,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,MAAM,iBAAiB,IAAI;AAAA,QAC/D,cAAc,CAAC,aAAa,iBAAiB,aAAa,oBAAoB;AAAA,QAC9E,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,IAAI;AAAA,QACxC,cAAc,CAAC,aAAa,eAAe;AAAA,QAC3C,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,cAAc,iBAAiB,aAAa;AAAA,QAChF,cAAc,CAAC,aAAa,yBAAyB,aAAa,wBAAwB;AAAA,QAC1F,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,IACF;AAED,UAAM,gBAAgB,eAAe,KAAK,SAAU,GAAG,GAAG;AACxD,aAAO,EAAE,gBAAgB,EAAE;AAAA,IACnC,CAAO;AACD,UAAM,gBAAgB,eAAe,KAAK,SAAU,GAAG,GAAG;AACxD,aAAO,EAAE,gBAAgB,EAAE;AAAA,IACnC,CAAO;AAED,aAAS,oBAAoB,aAAa,OAAO,QAAQ,UAAU;AACjE,UAAI;AACJ,UAAI;AAEJ,YAAM,UAAU,gBAAgB,YAAY,QAAQ,gBAAgB;AAEpE,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,MAAM,QAAQ,CAAC;AAErB,YAAI,CAAC,OAAO,IAAI,EAAE;AAAG;AACrB,YAAI,CAAC,IAAI,YAAY,SAAS,WAAW;AAAG;AAC5C,YAAI,YAAY,IAAI,iBAAiB,SAAS;AAAG;AACjD,YAAI,IAAI,mBAAmB,EAAE,aAAa,KAAK,KAAK,aAAa,MAAM;AAAI;AAE3E,2BAAmB,IAAI,iBAAiB,WAAW,IAAI,CAAC;AACxD,uBAAe,IAAI,aAAa,WAAW,IAAI,CAAC;AAEhD,eAAO,EAAE,kBAAkB,aAAc;AAAA,MAC1C;AAED,cAAQ,KAAK,oFAAoF;AAEjG,yBAAmB,iBAAiB;AACpC,qBAAe,aAAa;AAE5B,aAAO,EAAE,kBAAkB,aAAc;AAAA,IAC1C;AAED,aAAS,aAAa,OAAO;AAC3B,UAAI,SAAS;AAAG,eAAO;AAEvB,cAAQ,QAAS,QAAQ,OAAQ,KAAK,UAAU;AAAA,IACjD;AAGD,aAAS,OAAO,QAAQ;AACtB,UAAI,OAAO,WAAW;AAAG,eAAO,OAAO,CAAC;AAExC,UAAI,kBAAkB;AAEtB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,QAAQ,OAAO,CAAC;AACtB,2BAAmB,MAAM;AAAA,MAC1B;AAED,YAAM,SAAS,IAAI,WAAW,eAAe;AAE7C,UAAI,aAAa;AAEjB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,QAAQ,OAAO,CAAC;AACtB,eAAO,IAAI,OAAO,UAAU;AAE5B,sBAAc,MAAM;AAAA,MACrB;AAED,aAAO;AAAA,IACR;AAAA,EACF;AAiNH,SAAOA;AACT,GAAI;AAMJ,MAAM,uBAAuB,oBAAI,IAAI,CAACC,MAAU,YAAEU,MAAQ,UAAEC,MAAS,SAAA,CAAC;AAEtE,MAAM,aAAa;AAAA,EACjB,CAACC,SAA6B,6BAAA,GAAGZ,MAAU;AAAA,EAC3C,CAACa,SAA6B,6BAAA,GAAGb,MAAU;AAAA,EAC3C,CAACc,SAAwB,wBAAA,GAAGd,MAAU;AAAA,EACtC,CAACe,SAAuB,uBAAA,GAAGf,MAAU;AAAA,EAErC,CAACgB,SAAuB,uBAAA,GAAGN,MAAQ;AAAA,EACnC,CAACO,SAAuB,uBAAA,GAAGP,MAAQ;AAAA,EACnC,CAACQ,SAAoB,oBAAA,GAAGR,MAAQ;AAAA,EAChC,CAACS,SAAmB,mBAAA,GAAGT,MAAQ;AAAA,EAE/B,CAACU,SAAoB,oBAAA,GAAGT,MAAS;AAAA,EACjC,CAACU,SAAoB,oBAAA,GAAGV,MAAS;AAAA,EACjC,CAACW,SAAiB,iBAAA,GAAGX,MAAS;AAAA,EAC9B,CAACY,SAAkB,kBAAA,GAAGZ,MAAS;AAAA,EAE/B,CAACa,SAA6B,6BAAA,GAAGC,MAAoB;AAAA,EACrD,CAACC,SAA8B,8BAAA,GAAGD,MAAoB;AACxD;AAEA,MAAM,WAAW;AAAA,EACf,CAACb,SAA6B,6BAAA,GAAGe,MAAS;AAAA,EAC1C,CAACd,SAA6B,6BAAA,GAAGe,MAAa;AAAA,EAC9C,CAACd,SAAwB,wBAAA,GAAGvB,MAAgB;AAAA,EAC5C,CAACwB,SAAuB,uBAAA,GAAGxB,MAAgB;AAAA,EAE3C,CAACyB,SAAuB,uBAAA,GAAGW,MAAS;AAAA,EACpC,CAACV,SAAuB,uBAAA,GAAGW,MAAa;AAAA,EACxC,CAACV,SAAoB,oBAAA,GAAG3B,MAAgB;AAAA,EACxC,CAAC4B,SAAmB,mBAAA,GAAG5B,MAAgB;AAAA,EAEvC,CAAC6B,SAAoB,oBAAA,GAAGO,MAAS;AAAA,EACjC,CAACN,SAAoB,oBAAA,GAAGO,MAAa;AAAA,EACrC,CAACN,SAAiB,iBAAA,GAAG/B,MAAgB;AAAA,EACrC,CAACgC,SAAkB,kBAAA,GAAGhC,MAAgB;AAAA,EAEtC,CAACiC,SAA6B,6BAAA,GAAGjC,MAAgB;AAAA,EACjD,CAACmC,SAA8B,8BAAA,GAAGnC,MAAgB;AACpD;AAEA,eAAe,iBAAiB,WAAW;AACzC,QAAM,EAAE,SAAQ,IAAK;AAErB,MAAI,WAAW,QAAQ,MAAM,QAAW;AACtC,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC1D;AAID,MAAI;AAEJ,MAAI,UAAU,2BAA2BsC,oCAA2B;AAClE,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,QAAQ,OAAO,YAAY;AACrC,cAAMC,QAAO,IAAIC,oBAAa;AAC9B,cAAMD,MAAK,KAAM;AACjB,gBAAQA,KAAI;AAAA,MACpB,CAAO;AAAA,IACF;AAED,WAAO,MAAM;AAAA,EACd;AAID,QAAM,UAAU,CAAE;AAElB,WAAS,aAAa,GAAG,aAAa,UAAU,OAAO,QAAQ,cAAc;AAC3E,UAAM,aAAa,KAAK,IAAI,GAAG,UAAU,cAAc,UAAU;AACjE,UAAM,cAAc,KAAK,IAAI,GAAG,UAAU,eAAe,UAAU;AACnE,UAAM,aAAa,UAAU,aAAa,KAAK,IAAI,GAAG,UAAU,cAAc,UAAU,IAAI;AAE5F,UAAM,QAAQ,UAAU,OAAO,UAAU;AAEzC,QAAI;AAEJ,QAAI,UAAU,2BAA2BE,oCAA2B;AAClE,kBAAY,MAAM;AAAA,IACxB,WAAe,UAAU,2BAA2BH,oCAA2B;AACzE,kBAAY,KAAK,OAAO,MAAM,WAAW,MAAM,sBAAsB;AAAA,IAC3E,OAAW;AACL,YAAM,IAAI,MAAM,uDAAuD;AAAA,IACxE;AAED,QAAI;AAEJ,QAAI,SAAS,QAAQ,MAAMF,iBAAW;AACpC,aAAO,IAAI;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU,aAAa,aAAa;AAAA,MACrC;AAAA,IACF,WAAU,SAAS,QAAQ,MAAMC,qBAAe;AAC/C,aAAO,IAAI;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU,aAAa,YAAY;AAAA,MACpC;AAAA,IACP,OAAW;AACL,aAAO;AAAA,IACR;AAED,YAAQ,KAAK;AAAA,MACX;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IACb,CAAK;AAAA,EACF;AAED,MAAI;AAEJ,MAAI,qBAAqB,IAAI,WAAW,QAAQ,CAAC,GAAG;AAClD,cACE,UAAU,eAAe,IACrB,IAAIK,MAAW,YAAC,QAAQ,CAAC,EAAE,MAAM,UAAU,YAAY,UAAU,WAAW,IAC5E,IAAIC,4BAAc,QAAQ,CAAC,EAAE,MAAM,UAAU,YAAY,UAAU,aAAa,UAAU,UAAU;AAAA,EAC9G,OAAS;AACL,QAAI,UAAU,aAAa;AAAG,YAAM,IAAI,MAAM,2CAA2C;AAEzF,cAAU,IAAIzC,MAAAA,kBAAkB,SAAS,UAAU,YAAY,UAAU,WAAW;AAAA,EACrF;AAED,UAAQ,UAAU;AAElB,UAAQ,OAAO,SAAS,QAAQ;AAChC,UAAQ,SAAS,WAAW,QAAQ;AACpC,UAAQ,cAAc;AAEtB,QAAM,aAAa,gBAAgB,SAAS;AAC5C,MAAI,gBAAgB;AAAS,YAAQ,aAAa;AAAA;AAC7C,YAAQ,WAAW,eAAe,iBAAiB,eAAe;AAIvE,SAAO,QAAQ,QAAQ,OAAO;AAChC;AAEA,SAAS,gBAAgB,WAAW;AAClC,QAAM,MAAM,UAAU,qBAAqB,CAAC;AAE5C,MAAI,IAAI,mBAAmB0C,iCAAwB;AACjD,WAAO,IAAI,qBAAqBC,SAAoB,uBAAG,iBAAiB;AAAA,EAC5E,WAAa,IAAI,mBAAmBC,qCAA4B;AAC5D,WAAO,IAAI,qBAAqBD,SAAoB,uBAAG,sBAAsB;AAAA,EACjF,WAAa,IAAI,mBAAmBE,uCAA8B;AAC9D,WAAO;AAAA,EACX,OAAS;AACL,YAAQ,KAAK,mDAAmD,IAAI,iBAAiB;AACrF,WAAO;AAAA,EACR;AACH;;"}