import React, { useRef, useState, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Html, Points, PointMaterial } from '@react-three/drei';
import * as THREE from 'three';
import DetailModal from './DetailModal';

// Enhanced 3D CPU Component
const SimpleCPU: React.FC<{ position: [number, number, number]; onClick: () => void }> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);
  const meshRef = useRef<THREE.Group>(null);
  const cpuRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }

    // Pulsing effect for CPU
    if (cpuRef.current) {
      const material = cpuRef.current.material as THREE.MeshStandardMaterial;
      const pulse = Math.sin(state.clock.elapsedTime * 3) * 0.5 + 0.5;
      material.emissiveIntensity = hovered ? 0.3 + pulse * 0.2 : 0.1 + pulse * 0.1;
    }
  });

  return (
    <group
      ref={meshRef}
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* CPU Base */}
      <mesh ref={cpuRef}>
        <boxGeometry args={[3, 0.3, 3]} />
        <meshStandardMaterial
          color={hovered ? "#00ffff" : "#333333"}
          emissive={hovered ? "#003333" : "#000000"}
          roughness={0.2}
          metalness={0.8}
        />
      </mesh>
      
      {/* CPU Pins - simplified */}
      {Array.from({ length: 16 }, (_, i) => {
        const angle = (i / 16) * Math.PI * 2;
        const radius = 1.8;
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        return (
          <mesh key={i} position={[x, -0.2, z]}>
            <boxGeometry args={[0.1, 0.3, 0.1]} />
            <meshStandardMaterial color="#ffd700" metalness={1} roughness={0.1} />
          </mesh>
        );
      })}

      {/* Floating Label */}
      {hovered && (
        <Html position={[0, 2, 0]} center>
          <div style={{
            background: 'rgba(0, 255, 255, 0.1)',
            border: '1px solid #00ffff',
            borderRadius: '8px',
            padding: '10px',
            color: '#00ffff',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            ABOUT.exe - Click to explore
          </div>
        </Html>
      )}
    </group>
  );
};

// Simple RAM Component
const SimpleRAM: React.FC<{ position: [number, number, number]; onClick: () => void }> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <group
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* RAM Base */}
      <mesh>
        <boxGeometry args={[4, 0.2, 1]} />
        <meshStandardMaterial
          color={hovered ? "#00ff88" : "#1a4a1a"}
          emissive={hovered ? "#002200" : "#000000"}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Memory Chips */}
      {Array.from({ length: 8 }, (_, i) => (
        <mesh key={i} position={[i * 0.4 - 1.4, 0.2, 0]}>
          <boxGeometry args={[0.3, 0.2, 0.8]} />
          <meshStandardMaterial
            color="#000000"
            emissive={hovered ? "#001100" : "#000000"}
            roughness={0.1}
            metalness={0.9}
          />
        </mesh>
      ))}

      {hovered && (
        <Html position={[0, 1.5, 0]} center>
          <div style={{
            background: 'rgba(0, 255, 136, 0.1)',
            border: '1px solid #00ff88',
            borderRadius: '8px',
            padding: '10px',
            color: '#00ff88',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            PROJECTS.dll - Interactive showcase
          </div>
        </Html>
      )}
    </group>
  );
};

// Simple GPU Component
const SimpleGPU: React.FC<{ position: [number, number, number]; onClick: () => void }> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);
  const fanRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (fanRef.current) {
      fanRef.current.rotation.z += hovered ? 0.3 : 0.1;
    }
  });

  return (
    <group
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* GPU Base */}
      <mesh>
        <boxGeometry args={[5, 0.5, 2]} />
        <meshStandardMaterial
          color={hovered ? "#ff6b35" : "#2a2a2a"}
          emissive={hovered ? "#331100" : "#000000"}
          roughness={0.2}
          metalness={0.8}
        />
      </mesh>

      {/* Cooling Fan */}
      <mesh ref={fanRef} position={[0, 0.5, 0]}>
        <cylinderGeometry args={[0.8, 0.8, 0.1, 6]} />
        <meshStandardMaterial
          color="#666666"
          transparent
          opacity={0.7}
          metalness={0.5}
        />
      </mesh>

      {hovered && (
        <Html position={[0, 2, 0]} center>
          <div style={{
            background: 'rgba(255, 107, 53, 0.1)',
            border: '1px solid #ff6b35',
            borderRadius: '8px',
            padding: '10px',
            color: '#ff6b35',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            SKILLS.gpu - Technical abilities
          </div>
        </Html>
      )}
    </group>
  );
};

// Simple Network Card Component
const SimpleNetworkCard: React.FC<{ position: [number, number, number]; onClick: () => void }> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <group
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* Network Card Base */}
      <mesh>
        <boxGeometry args={[3, 0.2, 1.5]} />
        <meshStandardMaterial
          color={hovered ? "#4ecdc4" : "#1a3a3a"}
          emissive={hovered ? "#003333" : "#000000"}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Ethernet Port */}
      <mesh position={[1.2, 0.2, 0]}>
        <boxGeometry args={[0.6, 0.3, 0.8]} />
        <meshStandardMaterial color="#333333" metalness={0.8} roughness={0.2} />
      </mesh>

      {hovered && (
        <Html position={[0, 1.5, 0]} center>
          <div style={{
            background: 'rgba(78, 205, 196, 0.1)',
            border: '1px solid #4ecdc4',
            borderRadius: '8px',
            padding: '10px',
            color: '#4ecdc4',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            CONTACT.net - Get in touch
          </div>
        </Html>
      )}
    </group>
  );
};

// Enhanced PCB Board with more details
const SimplePCB: React.FC = () => {
  const boardRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (boardRef.current) {
      // Subtle breathing effect
      boardRef.current.position.y = -2 + Math.sin(state.clock.elapsedTime * 0.5) * 0.02;
    }
  });

  return (
    <group ref={boardRef}>
      {/* Main PCB Board */}
      <mesh position={[0, -2, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[25, 25]} />
        <meshStandardMaterial
          color="#0a4a0a"
          roughness={0.8}
          metalness={0.2}
        />
      </mesh>

      {/* Circuit Traces - Horizontal */}
      {Array.from({ length: 12 }, (_, i) => (
        <mesh key={`h-${i}`} position={[i * 2 - 11, -1.98, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <planeGeometry args={[0.08, 25]} />
          <meshStandardMaterial
            color="#00ff88"
            emissive="#002200"
            transparent
            opacity={0.8}
          />
        </mesh>
      ))}

      {/* Circuit Traces - Vertical */}
      {Array.from({ length: 12 }, (_, i) => (
        <mesh key={`v-${i}`} position={[0, -1.98, i * 2 - 11]} rotation={[-Math.PI / 2, 0, 0]}>
          <planeGeometry args={[25, 0.08]} />
          <meshStandardMaterial
            color="#00ff88"
            emissive="#002200"
            transparent
            opacity={0.8}
          />
        </mesh>
      ))}

      {/* Circuit Nodes */}
      {Array.from({ length: 20 }, (_, i) => {
        const x = (Math.random() - 0.5) * 20;
        const z = (Math.random() - 0.5) * 20;
        return (
          <mesh key={`node-${i}`} position={[x, -1.95, z]}>
            <cylinderGeometry args={[0.1, 0.1, 0.05]} />
            <meshStandardMaterial
              color="#ffd700"
              emissive="#332200"
              metalness={1}
              roughness={0.1}
            />
          </mesh>
        );
      })}

      {/* Small Components scattered around */}
      {Array.from({ length: 15 }, (_, i) => {
        const x = (Math.random() - 0.5) * 18;
        const z = (Math.random() - 0.5) * 18;
        const componentType = Math.floor(Math.random() * 3);
        return (
          <mesh key={`comp-${i}`} position={[x, -1.8, z]} rotation={[0, Math.random() * Math.PI, 0]}>
            {componentType === 0 && <boxGeometry args={[0.3, 0.1, 0.6]} />}
            {componentType === 1 && <cylinderGeometry args={[0.15, 0.15, 0.4]} />}
            {componentType === 2 && <boxGeometry args={[0.4, 0.1, 0.4]} />}
            <meshStandardMaterial
              color={componentType === 0 ? "#ff6b35" : componentType === 1 ? "#4ecdc4" : "#45b7d1"}
              emissive={componentType === 0 ? "#331100" : componentType === 1 ? "#003333" : "#001133"}
              roughness={0.3}
              metalness={0.7}
            />
          </mesh>
        );
      })}
    </group>
  );
};

// Floating Data Particles
const DataParticles: React.FC = () => {
  const pointsRef = useRef<THREE.Points>(null);

  const particles = useMemo(() => {
    const count = 500;
    const positions = new Float32Array(count * 3);

    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 30;
      positions[i3 + 1] = Math.random() * 15;
      positions[i3 + 2] = (Math.random() - 0.5) * 30;
    }

    return positions;
  }, []);

  useFrame((state) => {
    if (pointsRef.current) {
      const positions = pointsRef.current.geometry.attributes.position.array as Float32Array;

      for (let i = 0; i < positions.length; i += 3) {
        // Gentle floating motion
        positions[i + 1] += Math.sin(state.clock.elapsedTime + i) * 0.002;

        // Reset particles that go too high
        if (positions[i + 1] > 15) {
          positions[i + 1] = 0;
        }
      }

      pointsRef.current.geometry.attributes.position.needsUpdate = true;
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;
    }
  });

  return (
    <Points ref={pointsRef} positions={particles} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color="#00ffff"
        size={0.05}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.6}
      />
    </Points>
  );
};

const SimpleMicrochipScene: React.FC = () => {
  const [detailModal, setDetailModal] = useState<{
    isOpen: boolean;
    section: string | null;
  }>({
    isOpen: false,
    section: null
  });

  const handleComponentClick = (component: string) => {
    setDetailModal({ isOpen: true, section: component });
  };

  const closeDetailModal = () => {
    setDetailModal({ isOpen: false, section: null });
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      width: '100%', 
      height: '100%', 
      zIndex: 1,
      background: 'radial-gradient(ellipse at center, #001122 0%, #000000 70%)'
    }}>
      <Canvas
        camera={{ position: [0, 8, 15], fov: 75 }}
        style={{ background: 'transparent' }}
      >
        {/* Enhanced Lighting */}
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={1.2} color="#00ffff" />
        <pointLight position={[-10, 10, -10]} intensity={1} color="#00ff88" />
        <pointLight position={[0, -5, 0]} intensity={0.8} color="#ff6b35" />
        <pointLight position={[0, 15, 0]} intensity={0.6} color="#ffffff" />
        <spotLight
          position={[0, 20, 0]}
          angle={0.4}
          penumbra={1}
          intensity={0.8}
          color="#4ecdc4"
          castShadow
        />
        
        {/* PCB Environment */}
        <SimplePCB />

        {/* Floating Data Particles */}
        <DataParticles />

        {/* Portfolio Components */}
        <SimpleCPU position={[-6, 2, 4]} onClick={() => handleComponentClick('About')} />
        <SimpleRAM position={[6, 2, 4]} onClick={() => handleComponentClick('Projects')} />
        <SimpleGPU position={[-6, 2, -4]} onClick={() => handleComponentClick('Skills')} />
        <SimpleNetworkCard position={[6, 2, -4]} onClick={() => handleComponentClick('Contact')} />
        
        {/* Title */}
        <Text
          position={[0, 6, 0]}
          fontSize={1.5}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          SEMICONDUCTOR PORTFOLIO
        </Text>
        
        <Text
          position={[0, 4.5, 0]}
          fontSize={0.8}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          Navigate with mouse • Click components to explore
        </Text>
        
        {/* Camera Controls */}
        <OrbitControls
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          autoRotate={false}
          maxDistance={30}
          minDistance={5}
          maxPolarAngle={Math.PI / 2.2}
          minPolarAngle={Math.PI / 6}
        />
      </Canvas>
      
      {/* UI Instructions */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        color: '#00ffff',
        fontFamily: 'monospace',
        fontSize: '14px',
        background: 'rgba(0, 0, 0, 0.7)',
        padding: '10px',
        borderRadius: '8px',
        border: '1px solid #00ffff',
        backdropFilter: 'blur(10px)'
      }}>
        <div>🖱️ Mouse: Rotate view</div>
        <div>🔍 Scroll: Zoom in/out</div>
        <div>👆 Hover: See component info</div>
        <div>🖱️ Click: Open detailed component view</div>
      </div>

      <DetailModal
        isOpen={detailModal.isOpen}
        onClose={closeDetailModal}
        section={detailModal.section}
      />
    </div>
  );
};

export default SimpleMicrochipScene;
