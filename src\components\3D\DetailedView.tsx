import React, { useState, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Html, Text, OrbitControls, Box } from '@react-three/drei';
import * as THREE from 'three';
import styled from 'styled-components';

const ModalOverlay = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
`;

const ModalContent = styled.div`
  width: 90vw;
  height: 90vh;
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
  border: 2px solid #00ffff;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: 2px solid #ff6b35;
  color: #ff6b35;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  z-index: 1001;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 107, 53, 0.2);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
  }
`;

const InfoPanel = styled.div`
  position: absolute;
  left: 20px;
  top: 20px;
  width: 300px;
  height: calc(100% - 40px);
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #00ffff;
  border-radius: 8px;
  padding: 20px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  overflow-y: auto;
  backdrop-filter: blur(10px);
`;

interface DetailedViewProps {
  isOpen: boolean;
  onClose: () => void;
  section: 'about' | 'projects' | 'skills' | 'contact' | null;
}

// 3D Detailed CPU for About Section
const DetailedCPU: React.FC = () => {
  const groupRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;
    }
  });

  return (
    <group ref={groupRef}>
      {/* CPU Die */}
      <mesh>
        <boxGeometry args={[4, 0.2, 4]} />
        <meshStandardMaterial
          color="#1a1a1a"
          roughness={0.1}
          metalness={0.9}
        />
      </mesh>

      {/* Transistor Grid */}
      {Array.from({ length: 100 }, (_, i) => {
        const x = (i % 10) * 0.35 - 1.75;
        const z = Math.floor(i / 10) * 0.35 - 1.75;
        return (
          <mesh key={i} position={[x, 0.15, z]}>
            <boxGeometry args={[0.1, 0.1, 0.1]} />
            <meshStandardMaterial
              color="#00ffff"
              emissive="#003333"
              roughness={0.2}
              metalness={0.8}
            />
          </mesh>
        );
      })}

      {/* Cache Memory */}
      {Array.from({ length: 4 }, (_, i) => (
        <mesh key={i} position={[i * 0.8 - 1.2, 0.3, 1.8]}>
          <boxGeometry args={[0.6, 0.2, 0.4]} />
          <meshStandardMaterial
            color="#ff6b35"
            emissive="#331100"
            roughness={0.3}
            metalness={0.7}
          />
        </mesh>
      ))}

      {/* Floating Data Streams */}
      {Array.from({ length: 20 }, (_, i) => (
        <FloatingData key={i} index={i} />
      ))}
    </group>
  );
};

const FloatingData: React.FC<{ index: number }> = ({ index }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 2 + index) * 2 + 3;
      meshRef.current.rotation.z = state.clock.elapsedTime + index;
    }
  });

  return (
    <mesh
      ref={meshRef}
      position={[(Math.random() - 0.5) * 8, 3, (Math.random() - 0.5) * 8]}
    >
      <boxGeometry args={[0.1, 0.1, 0.1]} />
      <meshStandardMaterial
        color="#00ff88"
        emissive="#003300"
        transparent
        opacity={0.7}
      />
    </mesh>
  );
};

const DetailedView: React.FC<DetailedViewProps> = ({ isOpen, onClose, section }) => {
  const getContent = () => {
    switch (section) {
      case 'about':
        return {
          title: 'ABOUT.exe - System Information',
          content: (
            <div>
              <h3 style={{ color: '#00ffff', marginBottom: '15px' }}>Core Specifications</h3>
              <p style={{ marginBottom: '10px' }}>
                <strong>Architecture:</strong> Full-Stack Developer<br/>
                <strong>Cores:</strong> Frontend, Backend, 3D Graphics<br/>
                <strong>Cache:</strong> 5+ years experience<br/>
                <strong>Clock Speed:</strong> Fast learner, adaptive
              </p>
              
              <h3 style={{ color: '#00ffff', marginBottom: '15px', marginTop: '20px' }}>Instruction Set</h3>
              <p style={{ marginBottom: '10px' }}>
                • React.js & TypeScript execution<br/>
                • Three.js 3D rendering pipeline<br/>
                • Node.js backend processing<br/>
                • Database optimization algorithms<br/>
                • UI/UX design protocols
              </p>

              <h3 style={{ color: '#00ffff', marginBottom: '15px', marginTop: '20px' }}>Performance Metrics</h3>
              <p>
                Optimized for high-performance web applications with focus on 
                immersive user experiences and cutting-edge technology integration.
              </p>
            </div>
          ),
          scene: <DetailedCPU />
        };
      
      case 'projects':
        return {
          title: 'PROJECTS.dll - Memory Bank',
          content: (
            <div>
              <h3 style={{ color: '#00ff88', marginBottom: '15px' }}>Project Repository</h3>
              <div style={{ marginBottom: '15px' }}>
                <strong style={{ color: '#00ff88' }}>3D Portfolio Engine</strong><br/>
                Interactive semiconductor-themed portfolio built with React Three Fiber
              </div>
              <div style={{ marginBottom: '15px' }}>
                <strong style={{ color: '#00ff88' }}>Neural Network Visualizer</strong><br/>
                Real-time 3D visualization of AI training processes
              </div>
              <div style={{ marginBottom: '15px' }}>
                <strong style={{ color: '#00ff88' }}>Quantum Circuit Simulator</strong><br/>
                WebGL-based quantum computing visualization tool
              </div>
              <div style={{ marginBottom: '15px' }}>
                <strong style={{ color: '#00ff88' }}>Cyberpunk Dashboard</strong><br/>
                Futuristic data analytics platform with real-time updates
              </div>
            </div>
          ),
          scene: <DetailedCPU />
        };

      case 'skills':
        return {
          title: 'SKILLS.gpu - Processing Units',
          content: (
            <div>
              <h3 style={{ color: '#ff6b35', marginBottom: '15px' }}>Technical Stack</h3>
              <p style={{ marginBottom: '10px' }}>
                <strong>Frontend:</strong> React, TypeScript, Three.js<br/>
                <strong>Backend:</strong> Node.js, Python, GraphQL<br/>
                <strong>3D Graphics:</strong> WebGL, GLSL, Blender<br/>
                <strong>Databases:</strong> PostgreSQL, MongoDB<br/>
                <strong>Cloud:</strong> AWS, Docker, Kubernetes
              </p>
            </div>
          ),
          scene: <DetailedCPU />
        };

      case 'contact':
        return {
          title: 'CONTACT.net - Network Interface',
          content: (
            <div>
              <h3 style={{ color: '#4ecdc4', marginBottom: '15px' }}>Connection Protocols</h3>
              <p style={{ marginBottom: '10px' }}>
                <strong>Email:</strong> <EMAIL><br/>
                <strong>LinkedIn:</strong> /in/semiconductor-dev<br/>
                <strong>GitHub:</strong> /semiconductor-portfolio<br/>
                <strong>Status:</strong> Available for projects
              </p>
            </div>
          ),
          scene: <DetailedCPU />
        };

      default:
        return { title: '', content: null, scene: null };
    }
  };

  const { title, content, scene } = getContent();

  return (
    <ModalOverlay isOpen={isOpen}>
      <ModalContent>
        <CloseButton onClick={onClose}>CLOSE</CloseButton>
        
        <InfoPanel>
          <h2 style={{ color: '#00ffff', marginBottom: '20px', fontFamily: 'Orbitron' }}>
            {title}
          </h2>
          {content}
        </InfoPanel>

        <div style={{ marginLeft: '340px', height: '100%' }}>
          <Canvas camera={{ position: [0, 0, 8], fov: 75 }}>
            <ambientLight intensity={0.4} />
            <pointLight position={[10, 10, 10]} intensity={1} color="#00ffff" />
            <pointLight position={[-10, -10, -10]} intensity={0.5} color="#ffffff" />
            
            {scene}
            
            <OrbitControls
              enableZoom={true}
              enablePan={true}
              enableRotate={true}
              autoRotate={true}
              autoRotateSpeed={1}
            />
          </Canvas>
        </div>
      </ModalContent>
    </ModalOverlay>
  );
};

export default DetailedView;
