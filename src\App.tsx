import React from 'react';
import styled, { createGlobalStyle } from 'styled-components';
import MicrochipScene from './components/3D/MicrochipScene';

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Orbitron', 'Rajdhani', monospace;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
    line-height: 1.6;
    cursor: crosshair;
  }

  html {
    height: 100%;
  }

  #root {
    height: 100vh;
    width: 100vw;
  }

  canvas {
    display: block;
  }
`;

const AppContainer = styled.div`
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #000000;
`;

function App() {
  return (
    <AppContainer>
      <GlobalStyle />
      <MicrochipScene />
    </AppContainer>
  );
}

export default App;
