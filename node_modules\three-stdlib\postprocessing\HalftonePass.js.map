{"version": 3, "file": "HalftonePass.js", "sources": ["../../src/postprocessing/HalftonePass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport { ShaderMaterial, UniformsUtils, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { HalftoneShader } from '../shaders/HalftoneShader'\n\ntype HalftonePassParams = {\n  shape?: number\n  radius?: number\n  rotateR?: number\n  rotateB?: number\n  rotateG?: number\n  scatter?: number\n  blending?: number\n  blendingMode?: number\n  greyscale?: number\n  disable?: number\n}\n\n/**\n * RGB Halftone pass for three.js effects composer. Requires HalftoneShader.\n */\n\nclass HalftonePass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  public uniforms: any\n\n  constructor(width: number, height: number, params: HalftonePassParams) {\n    super()\n\n    if (HalftoneShader === undefined) {\n      console.error('THREE.HalftonePass requires HalftoneShader')\n    }\n\n    this.uniforms = UniformsUtils.clone(HalftoneShader.uniforms)\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      fragmentShader: HalftoneShader.fragmentShader,\n      vertexShader: HalftoneShader.vertexShader,\n    })\n\n    // set params\n    this.uniforms.width.value = width\n    this.uniforms.height.value = height\n\n    for (const key in params) {\n      if (params.hasOwnProperty(key) && this.uniforms.hasOwnProperty(key)) {\n        this.uniforms[key].value = params[key as keyof HalftonePassParams]\n      }\n    }\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    this.material.uniforms['tDiffuse'].value = readBuffer.texture\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public setSize(width: number, height: number): void {\n    this.uniforms.width.value = width\n    this.uniforms.height.value = height\n  }\n}\n\nexport { HalftonePass }\n"], "names": [], "mappings": ";;;;;;;;;AAqBA,MAAM,qBAAqB,KAAK;AAAA,EAM9B,YAAY,OAAe,QAAgB,QAA4B;AAC/D;AAND;AACA;AAEA;AAKL,QAAI,mBAAmB,QAAW;AAChC,cAAQ,MAAM,4CAA4C;AAAA,IAC5D;AAEA,SAAK,WAAW,cAAc,MAAM,eAAe,QAAQ;AACtD,SAAA,WAAW,IAAI,eAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,gBAAgB,eAAe;AAAA,MAC/B,cAAc,eAAe;AAAA,IAAA,CAC9B;AAGI,SAAA,SAAS,MAAM,QAAQ;AACvB,SAAA,SAAS,OAAO,QAAQ;AAE7B,eAAW,OAAO,QAAQ;AACpB,UAAA,OAAO,eAAe,GAAG,KAAK,KAAK,SAAS,eAAe,GAAG,GAAG;AACnE,aAAK,SAAS,GAAG,EAAE,QAAQ,OAAO,GAA+B;AAAA,MACnE;AAAA,IACF;AAEA,SAAK,SAAS,IAAI,eAAe,KAAK,QAAQ;AAAA,EAChD;AAAA,EAEO,OACL,UACA,aACA,YAEM;AACN,SAAK,SAAS,SAAS,UAAU,EAAE,QAAQ,WAAW;AAEtD,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AACpC,UAAI,KAAK;AAAO,iBAAS,MAAM;AAC1B,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA,EAEO,QAAQ,OAAe,QAAsB;AAC7C,SAAA,SAAS,MAAM,QAAQ;AACvB,SAAA,SAAS,OAAO,QAAQ;AAAA,EAC/B;AACF;"}