{"version": 3, "file": "KaleidoShader.js", "sources": ["../../src/shaders/KaleidoShader.ts"], "sourcesContent": ["/**\n * Kaleidoscope Shader\n * Radial reflection around center point\n * Ported from: http://pixelshaders.com/editor/\n * by <PERSON> / http://tobyschachman.com/\n *\n * sides: number of reflections\n * angle: initial angle in radians\n */\n\nexport const KaleidoShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    sides: { value: 6.0 },\n    angle: { value: 0.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float sides;\n    uniform float angle;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 p = vUv - 0.5;\n    \tfloat r = length(p);\n    \tfloat a = atan(p.y, p.x) + angle;\n    \tfloat tau = 2. * 3.1416 ;\n    \ta = mod(a, tau/sides);\n    \ta = abs(a - tau/sides/2.) ;\n    \tp = r * vec2(cos(a), sin(a));\n    \tvec4 color = texture2D(tDiffuse, p + 0.5);\n    \tgl_FragColor = color;\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAUO,MAAM,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,OAAO,EAAE,OAAO,EAAI;AAAA,IACpB,OAAO,EAAE,OAAO,EAAI;AAAA,EACtB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqB7B;"}