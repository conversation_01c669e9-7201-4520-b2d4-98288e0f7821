/**
 * MeshGouraudMaterial
 *
 * Lambert illumination model with <PERSON><PERSON>ud (per-vertex) shading
 *
 */

import { ShaderMaterial, ShaderMaterialParameters } from '../../../src/Three.js';

export class MeshGouraudMaterial extends ShaderMaterial {
    isMeshGouraudMaterial: true;
    type: 'MeshGouraudMaterial';

    constructor(parameters?: ShaderMaterialParameters);

    copy(source: MeshGouraudMaterial): this;
}
