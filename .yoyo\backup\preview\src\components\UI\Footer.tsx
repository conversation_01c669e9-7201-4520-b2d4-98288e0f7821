import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const FooterContainer = styled.footer`
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 20, 40, 0.9) 100%);
  border-top: 1px solid rgba(0, 255, 255, 0.3);
  padding: 3rem 2rem 1rem;
  position: relative;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
`;

const FooterSection = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SectionTitle = styled.h3`
  color: #00ffff;
  font-size: 1.2rem;
  font-family: 'Orbitron', monospace;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
`;

const SectionText = styled.p`
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
`;

const SocialLink = styled(motion.a)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  color: #00ffff;
  text-decoration: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: #00ffff;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
    transform: translateY(-2px);
  }
`;

const QuickLinks = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const QuickLink = styled.a`
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.3rem 0;

  &:hover {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    transform: translateX(5px);
  }
`;

const FooterBottom = styled.div`
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  padding-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const Copyright = styled.p`
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
`;

const TechStack = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const TechBadge = styled.span`
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #00ffff;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
`;

const CircuitPattern = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  background-image: 
    linear-gradient(90deg, #00ffff 1px, transparent 1px),
    linear-gradient(180deg, #00ffff 1px, transparent 1px);
  background-size: 30px 30px;
  pointer-events: none;
`;

const Footer: React.FC = () => {
  const socialLinks = [
    { icon: '📧', href: 'mailto:<EMAIL>', label: 'Email' },
    { icon: '💼', href: 'https://linkedin.com/in/microchip-dev', label: 'LinkedIn' },
    { icon: '🐙', href: 'https://github.com/microchip-dev', label: 'GitHub' },
    { icon: '🐦', href: 'https://twitter.com/microchip_dev', label: 'Twitter' },
    { icon: '📷', href: 'https://instagram.com/microchip.dev', label: 'Instagram' }
  ];

  const quickLinks = [
    { text: 'Home', href: '#home' },
    { text: 'About', href: '#about' },
    { text: 'Projects', href: '#projects' },
    { text: 'Skills', href: '#skills' },
    { text: 'Contact', href: '#contact' }
  ];

  const techStack = ['React', 'Three.js', 'TypeScript', 'Framer Motion', 'Styled Components'];

  const sectionVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <FooterContainer>
      <CircuitPattern />
      
      <FooterContent>
        <FooterSection
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <SectionTitle>MICROCHIP.DEV</SectionTitle>
          <SectionText>
            Crafting immersive digital experiences with cutting-edge technology. 
            Specializing in 3D web development, interactive design, and futuristic user interfaces.
          </SectionText>
          <SocialLinks>
            {socialLinks.map((link) => (
              <SocialLink
                key={link.label}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                title={link.label}
              >
                {link.icon}
              </SocialLink>
            ))}
          </SocialLinks>
        </FooterSection>

        <FooterSection
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={{ delay: 0.1 }}
        >
          <SectionTitle>Quick Links</SectionTitle>
          <QuickLinks>
            {quickLinks.map((link) => (
              <QuickLink key={link.text} href={link.href}>
                {link.text}
              </QuickLink>
            ))}
          </QuickLinks>
        </FooterSection>

        <FooterSection
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
        >
          <SectionTitle>Services</SectionTitle>
          <QuickLinks>
            <QuickLink href="#services">3D Web Development</QuickLink>
            <QuickLink href="#services">Interactive Design</QuickLink>
            <QuickLink href="#services">WebGL Applications</QuickLink>
            <QuickLink href="#services">UI/UX Design</QuickLink>
            <QuickLink href="#services">Technical Consulting</QuickLink>
          </QuickLinks>
        </FooterSection>

        <FooterSection
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
        >
          <SectionTitle>Built With</SectionTitle>
          <TechStack>
            {techStack.map((tech) => (
              <TechBadge key={tech}>{tech}</TechBadge>
            ))}
          </TechStack>
          <SectionText style={{ marginTop: '1rem' }}>
            This portfolio showcases the power of modern web technologies 
            in creating immersive 3D experiences.
          </SectionText>
        </FooterSection>
      </FooterContent>

      <FooterBottom>
        <Copyright>
          © 2024 MICROCHIP.DEV. All rights reserved. | Designed with ⚡ and built with 💙
        </Copyright>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <span style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '0.9rem' }}>
            Made with Three.js & React
          </span>
        </div>
      </FooterBottom>
    </FooterContainer>
  );
};

export default Footer;
