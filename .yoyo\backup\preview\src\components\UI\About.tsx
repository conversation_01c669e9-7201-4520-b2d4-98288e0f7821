import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const AboutContainer = styled.section`
  min-height: 100vh;
  padding: 100px 2rem 50px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 20, 40, 0.8) 100%);
  position: relative;
`;

const AboutContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const TextSection = styled.div`
  z-index: 10;
`;

const Title = styled(motion.h2)`
  font-size: 3rem;
  color: #00ffff;
  margin-bottom: 2rem;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
`;

const Description = styled(motion.p)`
  font-size: 1.2rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
`;

const TechGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
`;

const TechItem = styled(motion.div)`
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  color: #00ffff;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    transform: translateY(-5px);
  }
`;

const VisualSection = styled.div`
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CircuitSVG = styled(motion.svg)`
  width: 100%;
  height: 100%;
  max-width: 400px;
`;

const About: React.FC = () => {
  const technologies = [
    'React', 'TypeScript', 'Three.js', 'Node.js',
    'Python', 'WebGL', 'GLSL', 'Blender',
    'Docker', 'AWS', 'GraphQL', 'MongoDB'
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <AboutContainer id="about">
      <AboutContent>
        <TextSection>
          <Title
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            About Me
          </Title>
          
          <Description
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            I'm a passionate developer who specializes in creating immersive digital experiences 
            using cutting-edge technologies. With expertise in 3D graphics, web development, 
            and interactive design, I bring ideas to life through code.
          </Description>

          <Description
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            My work focuses on the intersection of technology and creativity, building 
            applications that not only function flawlessly but also provide engaging 
            user experiences that push the boundaries of what's possible on the web.
          </Description>

          <TechGrid
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {technologies.map((tech, index) => (
              <TechItem
                key={tech}
                variants={itemVariants}
                whileHover={{ scale: 1.05 }}
              >
                {tech}
              </TechItem>
            ))}
          </TechGrid>
        </TextSection>

        <VisualSection>
          <CircuitSVG
            viewBox="0 0 400 400"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
          >
            <defs>
              <linearGradient id="circuitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#00ffff" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#ffffff" stopOpacity="0.3" />
              </linearGradient>
            </defs>
            
            {/* Circuit paths */}
            <path
              d="M50 200 L150 200 L150 100 L250 100 L250 200 L350 200"
              stroke="url(#circuitGradient)"
              strokeWidth="2"
              fill="none"
            />
            <path
              d="M200 50 L200 150 L300 150 L300 250 L200 250 L200 350"
              stroke="url(#circuitGradient)"
              strokeWidth="2"
              fill="none"
            />
            
            {/* Circuit nodes */}
            <circle cx="150" cy="200" r="8" fill="#00ffff" opacity="0.8" />
            <circle cx="250" cy="100" r="8" fill="#00ffff" opacity="0.8" />
            <circle cx="200" cy="150" r="8" fill="#00ffff" opacity="0.8" />
            <circle cx="300" cy="250" r="8" fill="#00ffff" opacity="0.8" />
            
            {/* Microchip representation */}
            <rect
              x="175"
              y="175"
              width="50"
              height="50"
              fill="rgba(0, 255, 255, 0.2)"
              stroke="#00ffff"
              strokeWidth="2"
              rx="5"
            />
            <rect
              x="185"
              y="185"
              width="30"
              height="30"
              fill="rgba(0, 255, 255, 0.1)"
              stroke="#00ffff"
              strokeWidth="1"
              rx="2"
            />
          </CircuitSVG>
        </VisualSection>
      </AboutContent>
    </AboutContainer>
  );
};

export default About;
