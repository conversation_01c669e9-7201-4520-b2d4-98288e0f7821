import React, { useRef, useMemo, useState } from 'react';
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';
import { Points, PointMaterial, OrbitControls, Text, Html, Box, Plane } from '@react-three/drei';
import * as THREE from 'three';
import { motion } from 'framer-motion';
import DetailedView from './DetailedView';

// 3D PCB Board Component
const PCBBoard: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.1) * 0.02;
    }
  });

  return (
    <group>
      {/* Main PCB Board */}
      <mesh ref={meshRef} position={[0, -2, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[40, 40]} />
        <meshStandardMaterial
          color="#0a4a0a"
          roughness={0.8}
          metalness={0.2}
        />
      </mesh>

      {/* Circuit Traces */}
      {Array.from({ length: 20 }, (_, i) => (
        <CircuitTrace key={i} index={i} />
      ))}

      {/* Electronic Components */}
      {Array.from({ length: 15 }, (_, i) => (
        <ElectronicComponent key={i} index={i} />
      ))}
    </group>
  );
};

// Circuit Trace Component
const CircuitTrace: React.FC<{ index: number }> = ({ index }) => {
  const points = useMemo(() => {
    const curve = new THREE.CatmullRomCurve3([
      new THREE.Vector3(-15 + Math.random() * 30, -1.9, -15 + Math.random() * 30),
      new THREE.Vector3(-10 + Math.random() * 20, -1.9, -10 + Math.random() * 20),
      new THREE.Vector3(-5 + Math.random() * 10, -1.9, -5 + Math.random() * 10),
      new THREE.Vector3(5 + Math.random() * 10, -1.9, 5 + Math.random() * 10),
    ]);
    return curve.getPoints(50);
  }, [index]);

  return (
    <line>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={points.length}
          array={new Float32Array(points.flatMap(p => [p.x, p.y, p.z]))}
          itemSize={3}
        />
      </bufferGeometry>
      <lineBasicMaterial color="#00ff88" linewidth={2} />
    </line>
  );
};

// Electronic Component (Resistors, Capacitors, etc.)
const ElectronicComponent: React.FC<{ index: number }> = ({ index }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const position = useMemo(() => [
    (Math.random() - 0.5) * 30,
    -1.5,
    (Math.random() - 0.5) * 30
  ] as [number, number, number], [index]);

  const componentType = useMemo(() => Math.floor(Math.random() * 3), [index]);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
    }
  });

  return (
    <mesh ref={meshRef} position={position}>
      {componentType === 0 && <boxGeometry args={[0.5, 0.2, 1.5]} />}
      {componentType === 1 && <cylinderGeometry args={[0.3, 0.3, 1]} />}
      {componentType === 2 && <boxGeometry args={[1, 0.3, 1]} />}
      <meshStandardMaterial
        color={componentType === 0 ? "#ff6b35" : componentType === 1 ? "#4ecdc4" : "#45b7d1"}
        emissive={componentType === 0 ? "#331100" : componentType === 1 ? "#003333" : "#001133"}
        roughness={0.3}
        metalness={0.7}
      />
    </mesh>
  );
};

// 3D Portfolio Section - About Me (Microprocessor)
const AboutSection: React.FC<{
  position: [number, number, number];
  onClick: () => void;
}> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);
  const meshRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <group
      ref={meshRef}
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* CPU/Microprocessor */}
      <mesh>
        <boxGeometry args={[3, 0.3, 3]} />
        <meshStandardMaterial
          color={hovered ? "#00ffff" : "#333333"}
          emissive={hovered ? "#003333" : "#000000"}
          roughness={0.2}
          metalness={0.8}
        />
      </mesh>

      {/* CPU Pins */}
      {Array.from({ length: 64 }, (_, i) => {
        const side = Math.floor(i / 16);
        const pos = (i % 16) * 0.2 - 1.5;
        const positions: [number, number, number][] = [
          [pos, -0.2, 1.6], // top
          [pos, -0.2, -1.6], // bottom
          [1.6, -0.2, pos], // right
          [-1.6, -0.2, pos], // left
        ];
        return (
          <mesh key={i} position={positions[side]}>
            <boxGeometry args={[0.05, 0.3, 0.05]} />
            <meshStandardMaterial color="#ffd700" metalness={1} roughness={0.1} />
          </mesh>
        );
      })}

      {/* Floating Text */}
      {hovered && (
        <Html position={[0, 2, 0]} center>
          <div style={{
            background: 'rgba(0, 255, 255, 0.1)',
            border: '1px solid #00ffff',
            borderRadius: '8px',
            padding: '10px',
            color: '#00ffff',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            ABOUT.exe - Click to explore
          </div>
        </Html>
      )}
    </group>
  );
};

// 3D Portfolio Section - Projects (Memory Modules)
const ProjectsSection: React.FC<{
  position: [number, number, number];
  onClick: () => void;
}> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);
  const groupRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.05;
    }
  });

  return (
    <group
      ref={groupRef}
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* RAM Module Base */}
      <mesh>
        <boxGeometry args={[4, 0.2, 1]} />
        <meshStandardMaterial
          color={hovered ? "#00ff88" : "#1a4a1a"}
          emissive={hovered ? "#002200" : "#000000"}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Memory Chips */}
      {Array.from({ length: 8 }, (_, i) => (
        <mesh key={i} position={[i * 0.4 - 1.4, 0.2, 0]}>
          <boxGeometry args={[0.3, 0.2, 0.8]} />
          <meshStandardMaterial
            color="#000000"
            emissive={hovered ? "#001100" : "#000000"}
            roughness={0.1}
            metalness={0.9}
          />
        </mesh>
      ))}

      {/* Gold Contacts */}
      {Array.from({ length: 30 }, (_, i) => (
        <mesh key={i} position={[i * 0.13 - 1.9, -0.15, -0.4]}>
          <boxGeometry args={[0.08, 0.1, 0.1]} />
          <meshStandardMaterial color="#ffd700" metalness={1} roughness={0.1} />
        </mesh>
      ))}

      {hovered && (
        <Html position={[0, 1.5, 0]} center>
          <div style={{
            background: 'rgba(0, 255, 136, 0.1)',
            border: '1px solid #00ff88',
            borderRadius: '8px',
            padding: '10px',
            color: '#00ff88',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            PROJECTS.dll - Interactive showcase
          </div>
        </Html>
      )}
    </group>
  );
};

// 3D Portfolio Section - Skills (Graphics Card)
const SkillsSection: React.FC<{
  position: [number, number, number];
  onClick: () => void;
}> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);
  const fanRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (fanRef.current) {
      fanRef.current.rotation.z += hovered ? 0.3 : 0.1;
    }
  });

  return (
    <group
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* GPU Base */}
      <mesh>
        <boxGeometry args={[5, 0.5, 2]} />
        <meshStandardMaterial
          color={hovered ? "#ff6b35" : "#2a2a2a"}
          emissive={hovered ? "#331100" : "#000000"}
          roughness={0.2}
          metalness={0.8}
        />
      </mesh>

      {/* Cooling Fan */}
      <mesh ref={fanRef} position={[0, 0.5, 0]}>
        <cylinderGeometry args={[0.8, 0.8, 0.1, 6]} />
        <meshStandardMaterial
          color="#666666"
          transparent
          opacity={0.7}
          metalness={0.5}
        />
      </mesh>

      {/* Heat Sink */}
      {Array.from({ length: 10 }, (_, i) => (
        <mesh key={i} position={[i * 0.4 - 2, 0.3, 0.8]}>
          <boxGeometry args={[0.3, 0.4, 0.05]} />
          <meshStandardMaterial color="#888888" metalness={0.9} roughness={0.1} />
        </mesh>
      ))}

      {hovered && (
        <Html position={[0, 2, 0]} center>
          <div style={{
            background: 'rgba(255, 107, 53, 0.1)',
            border: '1px solid #ff6b35',
            borderRadius: '8px',
            padding: '10px',
            color: '#ff6b35',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            SKILLS.gpu - Technical abilities
          </div>
        </Html>
      )}
    </group>
  );
};

// 3D Portfolio Section - Contact (Network Interface Card)
const ContactSection: React.FC<{
  position: [number, number, number];
  onClick: () => void;
}> = ({ position, onClick }) => {
  const [hovered, setHovered] = useState(false);
  const ledRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (ledRef.current && ledRef.current.material) {
      const material = ledRef.current.material as THREE.MeshStandardMaterial;
      if (material.emissive) {
        material.emissive.setHex(
          hovered ? 0x00ff00 : Math.sin(state.clock.elapsedTime * 3) > 0 ? 0x00ff00 : 0x003300
        );
      }
    }
  });

  return (
    <group
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onClick={onClick}
    >
      {/* Network Card Base */}
      <mesh>
        <boxGeometry args={[3, 0.2, 1.5]} />
        <meshStandardMaterial
          color={hovered ? "#4ecdc4" : "#1a3a3a"}
          emissive={hovered ? "#003333" : "#000000"}
          roughness={0.3}
          metalness={0.7}
        />
      </mesh>

      {/* Ethernet Port */}
      <mesh position={[1.2, 0.2, 0]}>
        <boxGeometry args={[0.6, 0.3, 0.8]} />
        <meshStandardMaterial color="#333333" metalness={0.8} roughness={0.2} />
      </mesh>

      {/* Status LEDs */}
      {Array.from({ length: 4 }, (_, i) => (
        <mesh key={i} ref={i === 0 ? ledRef : undefined} position={[-1 + i * 0.3, 0.15, 0.5]}>
          <sphereGeometry args={[0.05]} />
          <meshStandardMaterial
            color={i === 0 ? "#00ff00" : i === 1 ? "#ffff00" : "#ff0000"}
            emissive={i === 0 ? "#003300" : i === 1 ? "#333300" : "#330000"}
          />
        </mesh>
      ))}

      {/* Antenna */}
      <mesh position={[-1.2, 0.8, 0]} rotation={[0, 0, Math.PI / 6]}>
        <cylinderGeometry args={[0.02, 0.02, 1]} />
        <meshStandardMaterial color="#888888" metalness={0.9} roughness={0.1} />
      </mesh>

      {hovered && (
        <Html position={[0, 1.5, 0]} center>
          <div style={{
            background: 'rgba(78, 205, 196, 0.1)',
            border: '1px solid #4ecdc4',
            borderRadius: '8px',
            padding: '10px',
            color: '#4ecdc4',
            fontFamily: 'monospace',
            fontSize: '14px',
            backdropFilter: 'blur(10px)',
            whiteSpace: 'nowrap'
          }}>
            CONTACT.net - Get in touch
          </div>
        </Html>
      )}
    </group>
  );
};

// Particle System for Electronic Effects
const ElectronFlow: React.FC = () => {
  const pointsRef = useRef<THREE.Points>(null);

  const particles = useMemo(() => {
    const count = 1000;
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);

    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 50;
      positions[i3 + 1] = Math.random() * 10 - 5;
      positions[i3 + 2] = (Math.random() - 0.5) * 50;

      colors[i3] = 0;
      colors[i3 + 1] = Math.random();
      colors[i3 + 2] = 1;
    }

    return { positions, colors };
  }, []);

  useFrame((state) => {
    if (pointsRef.current) {
      const positions = pointsRef.current.geometry.attributes.position.array as Float32Array;

      for (let i = 0; i < positions.length; i += 3) {
        positions[i] += Math.sin(state.clock.elapsedTime + i) * 0.01;
        positions[i + 2] += Math.cos(state.clock.elapsedTime + i) * 0.01;
      }

      pointsRef.current.geometry.attributes.position.needsUpdate = true;
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particles.positions.length / 3}
          array={particles.positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particles.colors.length / 3}
          array={particles.colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.1}
        vertexColors
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
};

const MicrochipScene: React.FC = () => {
  const [detailView, setDetailView] = useState<{
    isOpen: boolean;
    section: 'about' | 'projects' | 'skills' | 'contact' | null;
  }>({
    isOpen: false,
    section: null
  });

  const openDetailView = (section: 'about' | 'projects' | 'skills' | 'contact') => {
    setDetailView({ isOpen: true, section });
  };

  const closeDetailView = () => {
    setDetailView({ isOpen: false, section: null });
  };

  return (
    <>
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        background: 'radial-gradient(ellipse at center, #001122 0%, #000000 70%)'
      }}>
      <Canvas
        camera={{ position: [0, 8, 15], fov: 75 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting Setup */}
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#00ffff" />
        <pointLight position={[-10, 10, -10]} intensity={0.8} color="#00ff88" />
        <pointLight position={[0, -5, 0]} intensity={0.5} color="#ff6b35" />
        <spotLight
          position={[0, 20, 0]}
          angle={0.3}
          penumbra={1}
          intensity={1}
          color="#ffffff"
          castShadow
        />

        {/* Main 3D Environment */}
        <PCBBoard />
        <ElectronFlow />

        {/* 3D Portfolio Sections */}
        <AboutSection position={[-8, 2, 5]} onClick={() => openDetailView('about')} />
        <ProjectsSection position={[8, 2, 5]} onClick={() => openDetailView('projects')} />
        <SkillsSection position={[-8, 2, -5]} onClick={() => openDetailView('skills')} />
        <ContactSection position={[8, 2, -5]} onClick={() => openDetailView('contact')} />

        {/* 3D Navigation Text */}
        <Text
          position={[0, 6, 0]}
          fontSize={1.5}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          SEMICONDUCTOR PORTFOLIO
        </Text>

        <Text
          position={[0, 4.5, 0]}
          fontSize={0.8}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          Navigate with mouse • Hover components to interact
        </Text>

        {/* Camera Controls */}
        <OrbitControls
          enableZoom={true}
          enablePan={true}
          enableRotate={true}
          autoRotate={false}
          maxDistance={30}
          minDistance={5}
          maxPolarAngle={Math.PI / 2.2}
          minPolarAngle={Math.PI / 6}
        />
      </Canvas>

      {/* 3D UI Overlay */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        color: '#00ffff',
        fontFamily: 'monospace',
        fontSize: '14px',
        background: 'rgba(0, 0, 0, 0.7)',
        padding: '10px',
        borderRadius: '8px',
        border: '1px solid #00ffff',
        backdropFilter: 'blur(10px)'
      }}>
        <div>🖱️ Mouse: Rotate view</div>
        <div>🔍 Scroll: Zoom in/out</div>
        <div>👆 Hover: Interact with components</div>
        <div>🖱️ Click: Open detailed view</div>
      </div>

      <DetailedView
        isOpen={detailView.isOpen}
        onClose={closeDetailView}
        section={detailView.section}
      />
    </div>
    </>
  );
};

export default MicrochipScene;
