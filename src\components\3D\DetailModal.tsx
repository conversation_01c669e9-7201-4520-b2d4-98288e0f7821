import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Html } from '@react-three/drei';
import * as THREE from 'three';
import styled from 'styled-components';

const ModalOverlay = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
`;

const ModalContent = styled.div`
  width: 90vw;
  height: 90vh;
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
  border: 2px solid #00ffff;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
`;

const InfoPanel = styled.div`
  width: 350px;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  border-right: 1px solid #00ffff;
  padding: 20px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  overflow-y: auto;
`;

const ScenePanel = styled.div`
  flex: 1;
  height: 100%;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: 2px solid #ff6b35;
  color: #ff6b35;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  z-index: 1001;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 107, 53, 0.2);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
  }
`;

const Title = styled.h2`
  color: #00ffff;
  font-family: 'Orbitron', monospace;
  margin-bottom: 20px;
  font-size: 1.5rem;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
`;

const Section = styled.div`
  margin-bottom: 20px;
`;

const SectionTitle = styled.h3`
  color: #00ff88;
  margin-bottom: 10px;
  font-size: 1.2rem;
`;

const DetailText = styled.p`
  line-height: 1.6;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.9);
`;

// 3D Detailed CPU Animation
const DetailedCPU: React.FC = () => {
  const groupRef = useRef<THREE.Group>(null);
  const coreRefs = useRef<THREE.Mesh[]>([]);

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.3;
    }
    
    // Animate individual cores
    coreRefs.current.forEach((core, index) => {
      if (core) {
        const offset = index * 0.5;
        core.position.y = Math.sin(state.clock.elapsedTime * 2 + offset) * 0.1;
        const material = core.material as THREE.MeshStandardMaterial;
        material.emissiveIntensity = Math.sin(state.clock.elapsedTime * 3 + offset) * 0.3 + 0.4;
      }
    });
  });

  return (
    <group ref={groupRef}>
      {/* CPU Base */}
      <mesh>
        <boxGeometry args={[4, 0.4, 4]} />
        <meshStandardMaterial
          color="#1a1a1a"
          roughness={0.1}
          metalness={0.9}
        />
      </mesh>

      {/* CPU Cores */}
      {Array.from({ length: 16 }, (_, i) => {
        const x = (i % 4) * 0.8 - 1.2;
        const z = Math.floor(i / 4) * 0.8 - 1.2;
        return (
          <mesh
            key={i}
            ref={(el) => { if (el) coreRefs.current[i] = el; }}
            position={[x, 0.3, z]}
          >
            <boxGeometry args={[0.3, 0.2, 0.3]} />
            <meshStandardMaterial
              color="#00ffff"
              emissive="#003333"
              roughness={0.2}
              metalness={0.8}
            />
          </mesh>
        );
      })}

      {/* Cache Memory */}
      {Array.from({ length: 4 }, (_, i) => (
        <mesh key={i} position={[i * 0.8 - 1.2, 0.5, 2.2]}>
          <boxGeometry args={[0.6, 0.3, 0.4]} />
          <meshStandardMaterial
            color="#ff6b35"
            emissive="#331100"
            roughness={0.3}
            metalness={0.7}
          />
        </mesh>
      ))}

      {/* Pins */}
      {Array.from({ length: 64 }, (_, i) => {
        const side = Math.floor(i / 16);
        const pos = (i % 16) * 0.25 - 1.875;
        const positions: [number, number, number][] = [
          [pos, -0.3, 2.2], // top
          [pos, -0.3, -2.2], // bottom
          [2.2, -0.3, pos], // right
          [-2.2, -0.3, pos], // left
        ];
        return (
          <mesh key={i} position={positions[side]}>
            <boxGeometry args={[0.05, 0.4, 0.05]} />
            <meshStandardMaterial color="#ffd700" metalness={1} roughness={0.1} />
          </mesh>
        );
      })}
    </group>
  );
};

interface DetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  section: string | null;
}

const DetailModal: React.FC<DetailModalProps> = ({ isOpen, onClose, section }) => {
  const getContent = () => {
    switch (section) {
      case 'About':
        return {
          title: 'ABOUT.exe - System Information',
          sections: [
            {
              title: 'Core Specifications',
              content: `Architecture: Full-Stack Developer
Cores: Frontend, Backend, 3D Graphics
Cache: 5+ years experience
Clock Speed: Fast learner, adaptive
Instruction Set: Modern web technologies`
            },
            {
              title: 'Processing Capabilities',
              content: `• React.js & TypeScript execution
• Three.js 3D rendering pipeline
• Node.js backend processing
• Database optimization algorithms
• UI/UX design protocols`
            },
            {
              title: 'Performance Metrics',
              content: `Optimized for high-performance web applications with focus on immersive user experiences and cutting-edge technology integration. Specialized in creating interactive 3D environments and modern web interfaces.`
            }
          ]
        };
      
      case 'Projects':
        return {
          title: 'PROJECTS.dll - Memory Bank',
          sections: [
            {
              title: 'Featured Projects',
              content: `3D Portfolio Engine - Interactive semiconductor-themed portfolio
Neural Network Visualizer - Real-time 3D AI training visualization
Quantum Circuit Simulator - WebGL quantum computing tool
Cyberpunk Dashboard - Futuristic data analytics platform`
            },
            {
              title: 'Technical Stack',
              content: `React Three Fiber, WebGL, GLSL Shaders
Real-time data processing and visualization
Interactive 3D environments and animations
Modern web technologies and frameworks`
            }
          ]
        };

      case 'Skills':
        return {
          title: 'SKILLS.gpu - Processing Units',
          sections: [
            {
              title: 'Frontend Technologies',
              content: `React.js, TypeScript, Three.js, WebGL
CSS3, Styled Components, Framer Motion
Responsive design, Progressive Web Apps`
            },
            {
              title: 'Backend & Database',
              content: `Node.js, Python, GraphQL, REST APIs
PostgreSQL, MongoDB, Redis
Cloud services: AWS, Docker, Kubernetes`
            },
            {
              title: '3D Graphics & Animation',
              content: `Three.js, WebGL, GLSL Shaders
Blender, 3D modeling and animation
Real-time rendering and optimization`
            }
          ]
        };

      case 'Contact':
        return {
          title: 'CONTACT.net - Network Interface',
          sections: [
            {
              title: 'Connection Protocols',
              content: `Email: <EMAIL>
LinkedIn: /in/semiconductor-dev
GitHub: /semiconductor-portfolio
Status: Available for projects`
            },
            {
              title: 'Collaboration',
              content: `Open to innovative projects and collaborations
Specializing in 3D web experiences
Remote work capabilities worldwide
Fast response time and reliable communication`
            }
          ]
        };

      default:
        return { title: '', sections: [] };
    }
  };

  const { title, sections } = getContent();

  return (
    <ModalOverlay isOpen={isOpen}>
      <ModalContent>
        <CloseButton onClick={onClose}>CLOSE</CloseButton>
        
        <InfoPanel>
          <Title>{title}</Title>
          {sections.map((section, index) => (
            <Section key={index}>
              <SectionTitle>{section.title}</SectionTitle>
              <DetailText style={{ whiteSpace: 'pre-line' }}>
                {section.content}
              </DetailText>
            </Section>
          ))}
        </InfoPanel>

        <ScenePanel>
          <Canvas camera={{ position: [0, 0, 8], fov: 75 }}>
            <ambientLight intensity={0.4} />
            <pointLight position={[10, 10, 10]} intensity={1} color="#00ffff" />
            <pointLight position={[-10, -10, -10]} intensity={0.5} color="#ffffff" />
            <pointLight position={[0, 5, 5]} intensity={0.8} color="#ff6b35" />
            
            <DetailedCPU />
            
            <Text
              position={[0, -4, 0]}
              fontSize={0.8}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              {section?.toUpperCase()} COMPONENT ANALYSIS
            </Text>
            
            <OrbitControls
              enableZoom={true}
              enablePan={true}
              enableRotate={true}
              autoRotate={true}
              autoRotateSpeed={2}
            />
          </Canvas>
        </ScenePanel>
      </ModalContent>
    </ModalOverlay>
  );
};

export default DetailModal;
